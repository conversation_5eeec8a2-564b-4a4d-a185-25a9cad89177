<?php
// بدء جلسة
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// استيراد ملف الاتصال بقاعدة البيانات
require_once 'config/db_connection.php';

// استعلام لجلب إحصائيات الخزنة
$today = date('Y-m-d');

// رصيد الخزنة الحالي
$current_balance_query = "SELECT 
                            (SELECT COALESCE(SUM(amount), 0) FROM cashbox WHERE type IN ('deposit', 'sales')) - 
                            (SELECT COALESCE(SUM(amount), 0) FROM cashbox WHERE type IN ('withdraw', 'expense'))
                          AS balance";
$current_balance_result = mysqli_query($conn, $current_balance_query);
$current_balance = mysqli_fetch_assoc($current_balance_result)['balance'] ?? 0;

// إيرادات اليوم
$today_income_query = "SELECT COALESCE(SUM(amount), 0) as total FROM cashbox WHERE type IN ('deposit', 'sales') AND DATE(created_at) = '$today'";
$today_income_result = mysqli_query($conn, $today_income_query);
$today_income = mysqli_fetch_assoc($today_income_result)['total'] ?? 0;

// مصروفات اليوم
$today_expense_query = "SELECT COALESCE(SUM(amount), 0) as total FROM cashbox WHERE type IN ('withdraw', 'expense') AND DATE(created_at) = '$today'";
$today_expense_result = mysqli_query($conn, $today_expense_query);
$today_expense = mysqli_fetch_assoc($today_expense_result)['total'] ?? 0;

// إجمالي الأرصدة المحفوظة للعملاء
$total_wallet_query = "SELECT COALESCE(SUM(wallet_balance), 0) as total FROM customers WHERE wallet_balance > 0";
$total_wallet_result = mysqli_query($conn, $total_wallet_query);
$total_wallet = mysqli_fetch_assoc($total_wallet_result)['total'] ?? 0;

// إجمالي الديون
$total_debt_query = "SELECT COALESCE(SUM(debt_balance), 0) as total FROM customers WHERE debt_balance > 0";
$total_debt_result = mysqli_query($conn, $total_debt_query);
$total_debt = mysqli_fetch_assoc($total_debt_result)['total'] ?? 0;

// استعلام لجلب حركات الخزنة
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-d');
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-d');

$transactions_query = "SELECT * FROM cashbox";

if (!empty($filter)) {
    $transactions_query .= " WHERE type = '$filter'";
} else {
    $transactions_query .= " WHERE 1=1";
}

if (!empty($date_from) && !empty($date_to)) {
    $transactions_query .= " AND DATE(created_at) BETWEEN '$date_from' AND '$date_to'";
}

$transactions_query .= " ORDER BY created_at DESC LIMIT 50";
$transactions_result = mysqli_query($conn, $transactions_query);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الخزنة - نظام إدارة المتجر</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- ApexCharts -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --whatsapp-color: #25D366;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --sidebar-width: 250px;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fb;
            color: #333;
            overflow-x: hidden;
        }
        
        /* تصميم الشريط الجانبي */
        .sidebar {
            height: 100vh;
            width: var(--sidebar-width);
            position: fixed;
            top: 0;
            right: 0;
            background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding-top: 20px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            z-index: 1000;
        }
        
        .sidebar .logo {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .logo h3 {
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 15px;
            border-radius: 8px;
            transition: all 0.3s;
            font-size: 16px;
        }
        
        .sidebar .nav-link:hover, 
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar .nav-link i {
            margin-left: 10px;
            width: 25px;
            text-align: center;
        }
        
        /* تصميم المحتوى الرئيسي */
        .main-content {
            margin-right: var(--sidebar-width);
            padding: 20px;
            transition: all 0.3s;
        }
        
        /* شريط التنقل العلوي */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        
        .search-bar {
            background-color: #f8f9fa;
            border-radius: 30px;
            padding: 8px 20px;
            border: 1px solid #eaeaea;
        }
        
        .search-bar input {
            background: transparent;
            border: none;
            width: 100%;
            padding: 5px 10px;
        }
        
        .search-bar input:focus {
            outline: none;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #f94144;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* بطاقات الإحصائيات */
        .stat-card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .stat-card .card-title {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-card .card-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .stat-card .card-footer {
            background: transparent;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 13px;
            padding: 8px 15px;
        }
        
        /* تصميم قسم الخزنة */
        .cashbox-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .balance-card {
            background-color: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .balance-card .balance-title {
            font-size: 16px;
            color: #6c757d;
            margin-bottom: 10px;
        }
        
        .balance-card .balance-value {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .balance-card .balance-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        .transaction-table {
            margin-top: 20px;
        }
        
        .transaction-table .transaction-type {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }
        
        .transaction-table .transaction-type.deposit {
            background-color: var(--success-color);
        }
        
        .transaction-table .transaction-type.withdraw {
            background-color: var(--danger-color);
        }
        
        .transaction-table .transaction-type.sales {
            background-color: var(--primary-color);
        }
        
        .transaction-table .transaction-type.expense {
            background-color: var(--warning-color);
        }
        
        .transaction-filters {
            margin-bottom: 20px;
        }
        
        .transaction-summary {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .summary-item {
            flex: 1;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .summary-item.income {
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
        }
        
        .summary-item.expense {
            background-color: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
        }
        
        .summary-item.balance {
            background-color: rgba(67, 97, 238, 0.1);
            color: var(--primary-color);
        }
        
        .summary-item .summary-title {
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .summary-item .summary-value {
            font-size: 20px;
            font-weight: 700;
        }
        
        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
                overflow: hidden;
            }
            
            .sidebar .logo h3, .sidebar .nav-link span {
                display: none;
            }
            
            .sidebar .nav-link {
                text-align: center;
                margin: 5px 10px;
            }
            
            .sidebar .nav-link i {
                margin-left: 0;
            }
            
            .main-content {
                margin-right: 70px;
            }
            
            .transaction-summary {
                flex-direction: column;
                gap: 10px;
            }
            
            .balance-card .balance-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-store"></i> متجرنا</h3>
        </div>
        <ul class="nav flex-column mt-4">
            <li class="nav-item">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales.php">
                    <i class="fas fa-shopping-cart"></i>
                    <span>المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales_management.php">
                    <i class="fas fa-chart-line"></i>
                    <span>إدارة المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="products.php">
                    <i class="fas fa-box"></i>
                    <span>المنتجات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customers.php">
                    <i class="fas fa-users"></i>
                    <span>العملاء</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="suppliers.php">
                    <i class="fas fa-truck"></i>
                    <span>الموردين</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="purchases.php">
                    <i class="fas fa-shopping-basket"></i>
                    <span>المشتريات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="expenses.php">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>المصروفات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="cashbox.php">
                    <i class="fas fa-cash-register"></i>
                    <span>الخزنة</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="financial.php">
                    <i class="fas fa-chart-pie"></i>
                    <span>الإدارة المالية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="barcode.php">
                    <i class="fas fa-barcode"></i>
                    <span>مولد الباركود</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="settings.php">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط التنقل العلوي -->
        <div class="top-navbar">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4 class="mb-0">الخزنة</h4>
                    <p class="mb-0 text-muted">مرحباً بعودتك، <?php echo $_SESSION['username']; ?>!</p>
                </div>
                <div class="col-md-6 d-flex justify-content-end">
                    <div class="d-flex align-items-center">
                        <div class="search-bar me-3">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="بحث...">
                        </div>
                        <div class="position-relative me-3">
                            <button class="btn btn-light rounded-circle">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-light rounded-circle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.php">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="row">
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-primary-subtle text-primary me-3">
                            <i class="fas fa-cash-register"></i>
                        </div>
                        <div>
                            <div class="card-title">رصيد الخزنة</div>
                            <div class="card-value"><?php echo number_format($current_balance, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-sync-alt"></i> آخر تحديث: <?php echo date('Y-m-d H:i'); ?>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-success-subtle text-success me-3">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        <div>
                            <div class="card-title">إيرادات اليوم</div>
                            <div class="card-value"><?php echo number_format($today_income, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-calendar-day"></i> <?php echo date('Y-m-d'); ?>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-danger-subtle text-danger me-3">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <div>
                            <div class="card-title">مصروفات اليوم</div>
                            <div class="card-value"><?php echo number_format($today_expense, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-calendar-day"></i> <?php echo date('Y-m-d'); ?>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-info-subtle text-info me-3">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div>
                            <div class="card-title">أموال خارج الخزنة</div>
                            <div class="card-value"><?php echo number_format($total_wallet + $total_debt, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-info-circle"></i> أرصدة العملاء + الديون
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الخزنة -->
        <div class="row">
            <div class="col-md-4">
                <div class="balance-card">
                    <div class="balance-title">رصيد الخزنة الحالي</div>
                    <div class="balance-value"><?php echo number_format($current_balance, 2); ?> ر.س</div>
                    <div class="balance-actions">
                        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#depositModal">
                            <i class="fas fa-plus me-2"></i> إيداع
                        </button>
                        <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#withdrawModal">
                            <i class="fas fa-minus me-2"></i> سحب
                        </button>
                    </div>
                </div>
                
                <div class="cashbox-container">
                    <h5 class="mb-3">تفاصيل الأموال خارج الخزنة</h5>
                    <div class="list-group">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-wallet text-success me-2"></i>
                                أرصدة العملاء المحفوظة
                            </div>
                            <span class="badge bg-success rounded-pill"><?php echo number_format($total_wallet, 2); ?> ر.س</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-hand-holding-usd text-danger me-2"></i>
                                ديون العملاء
                            </div>
                            <span class="badge bg-danger rounded-pill"><?php echo number_format($total_debt, 2); ?> ر.س</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-calculator text-primary me-2"></i>
                                الإجمالي
                            </div>
                            <span class="badge bg-primary rounded-pill"><?php echo number_format($total_wallet + $total_debt, 2); ?> ر.س</span>
                        </div>
                    </div>
                </div>
                
                <div class="cashbox-container">
                    <h5 class="mb-3">الرسم البياني</h5>
                    <div id="cashflowChart" style="height: 300px;"></div>
                </div>
            </div>
            
            <div class="col-md-8">
                <div class="cashbox-container">
                    <h5 class="mb-3">حركات الخزنة</h5>
                    
                    <div class="transaction-filters">
                        <form action="" method="get" class="row g-3">
                            <div class="col-md-3">
                                <label for="filter" class="form-label">نوع العملية</label>
                                <select class="form-select" id="filter" name="filter">
                                    <option value="" <?php echo $filter == '' ? 'selected' : ''; ?>>الكل</option>
                                    <option value="deposit" <?php echo $filter == 'deposit' ? 'selected' : ''; ?>>إيداع</option>
                                    <option value="withdraw" <?php echo $filter == 'withdraw' ? 'selected' : ''; ?>>سحب</option>
                                    <option value="sales" <?php echo $filter == 'sales' ? 'selected' : ''; ?>>مبيعات</option>
                                    <option value="expense" <?php echo $filter == 'expense' ? 'selected' : ''; ?>>مصروفات</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="date_from" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-filter me-2"></i> تطبيق
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="transaction-summary">
                        <?php
                        // حساب إجمالي الإيرادات والمصروفات للفترة المحددة
                        $period_income_query = "SELECT COALESCE(SUM(amount), 0) as total FROM cashbox WHERE type IN ('deposit', 'sales')";
                        $period_expense_query = "SELECT COALESCE(SUM(amount), 0) as total FROM cashbox WHERE type IN ('withdraw', 'expense')";
                        
                        if (!empty($date_from) && !empty($date_to)) {
                            $period_income_query .= " AND DATE(created_at) BETWEEN '$date_from' AND '$date_to'";
                            $period_expense_query .= " AND DATE(created_at) BETWEEN '$date_from' AND '$date_to'";
                        }
                        
                        $period_income_result = mysqli_query($conn, $period_income_query);
                        $period_expense_result = mysqli_query($conn, $period_expense_query);
                        
                        $period_income = mysqli_fetch_assoc($period_income_result)['total'] ?? 0;
                        $period_expense = mysqli_fetch_assoc($period_expense_result)['total'] ?? 0;
                        $period_balance = $period_income - $period_expense;
                        ?>
                        <div class="summary-item income">
                            <div class="summary-title">إجمالي الإيرادات</div>
                            <div class="summary-value"><?php echo number_format($period_income, 2); ?> ر.س</div>
                        </div>
                        <div class="summary-item expense">
                            <div class="summary-title">إجمالي المصروفات</div>
                            <div class="summary-value"><?php echo number_format($period_expense, 2); ?> ر.س</div>
                        </div>
                        <div class="summary-item balance">
                            <div class="summary-title">صافي الحركة</div>
                            <div class="summary-value"><?php echo number_format($period_balance, 2); ?> ر.س</div>
                        </div>
                    </div>
                    
                    <div class="transaction-table">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>النوع</th>
                                        <th>المبلغ</th>
                                        <th>الملاحظات</th>
                                        <th>الفاتورة</th>
                                        <th>المستخدم</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    // عرض حركات الخزنة
                                    if (mysqli_num_rows($transactions_result) > 0) {
                                        while ($transaction = mysqli_fetch_assoc($transactions_result)) {
                                            // تحديد نوع العملية
                                            $type_class = '';
                                            $type_icon = '';
                                            $type_text = '';
                                            
                                            switch ($transaction['type']) {
                                                case 'deposit':
                                                    $type_class = 'deposit';
                                                    $type_icon = 'fas fa-plus';
                                                    $type_text = 'إيداع';
                                                    break;
                                                case 'withdraw':
                                                    $type_class = 'withdraw';
                                                    $type_icon = 'fas fa-minus';
                                                    $type_text = 'سحب';
                                                    break;
                                                case 'sales':
                                                    $type_class = 'sales';
                                                    $type_icon = 'fas fa-shopping-cart';
                                                    $type_text = 'مبيعات';
                                                    break;
                                                case 'expense':
                                                    $type_class = 'expense';
                                                    $type_icon = 'fas fa-file-invoice-dollar';
                                                    $type_text = 'مصروفات';
                                                    break;
                                                default:
                                                    $type_class = 'deposit';
                                                    $type_icon = 'fas fa-exchange-alt';
                                                    $type_text = $transaction['type'];
                                            }
                                            
                                            echo '<tr>';
                                            echo '<td>' . date('Y-m-d H:i', strtotime($transaction['created_at'])) . '</td>';
                                            echo '<td>';
                                            echo '<div class="d-flex align-items-center">';
                                            echo '<div class="transaction-type ' . $type_class . '">';
                                            echo '<i class="' . $type_icon . '"></i>';
                                            echo '</div>';
                                            echo '<span class="ms-2">' . $type_text . '</span>';
                                            echo '</div>';
                                            echo '</td>';
                                            echo '<td class="' . ($transaction['type'] == 'deposit' || $transaction['type'] == 'sales' ? 'text-success' : 'text-danger') . ' fw-bold">';
                                            echo ($transaction['type'] == 'deposit' || $transaction['type'] == 'sales' ? '+' : '-') . ' ' . number_format($transaction['amount'], 2) . ' ر.س';
                                            echo '</td>';
                                            echo '<td>' . $transaction['note'] . '</td>';
                                            echo '<td>';
                                            if (!empty($transaction['related_invoice'])) {
                                                echo '<a href="sales_management.php?invoice=' . $transaction['related_invoice'] . '" class="btn btn-sm btn-outline-primary">';
                                                echo '<i class="fas fa-file-invoice me-1"></i> #' . $transaction['related_invoice'];
                                                echo '</a>';
                                            } else {
                                                echo '-';
                                            }
                                            echo '</td>';
                                            echo '<td>' . ($_SESSION['username'] ?? 'النظام') . '</td>';
                                            echo '</tr>';
                                        }
                                    } else {
                                        echo '<tr><td colspan="6" class="text-center">لا توجد حركات للفترة المحددة</td></tr>';
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال الإيداع -->
    <div class="modal fade" id="depositModal" tabindex="-1" aria-labelledby="depositModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="depositModalLabel">إيداع في الخزنة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="depositForm">
                        <div class="mb-3">
                            <label for="depositAmount" class="form-label">المبلغ</label>
                            <input type="number" class="form-control" id="depositAmount" min="0" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label for="depositNote" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="depositNote" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" id="saveDeposit">إيداع</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال السحب -->
    <div class="modal fade" id="withdrawModal" tabindex="-1" aria-labelledby="withdrawModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="withdrawModalLabel">سحب من الخزنة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="withdrawForm">
                        <div class="mb-3">
                            <label for="withdrawAmount" class="form-label">المبلغ</label>
                            <input type="number" class="form-control" id="withdrawAmount" min="0" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label for="withdrawReason" class="form-label">سبب السحب</label>
                            <select class="form-select" id="withdrawReason" required>
                                <option value="">اختر سبب السحب</option>
                                <option value="expense">مصروفات</option>
                                <option value="salary">رواتب</option>
                                <option value="purchase">مشتريات</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="withdrawNote" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="withdrawNote" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="saveWithdraw">سحب</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // رسم بياني للتدفق النقدي
            var cashflowOptions = {
                series: [{
                    name: 'الإيرادات',
                    data: [3100, 4000, 2800, 5100, 4200, 3800, 5000]
                }, {
                    name: 'المصروفات',
                    data: [1100, 3200, 2100, 2000, 1500, 1800, 2700]
                }],
                chart: {
                    type: 'bar',
                    height: 300,
                    stacked: false,
                    toolbar: {
                        show: false
                    },
                    fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif'
                },
                plotOptions: {
                    bar: {
                        horizontal: false,
                        columnWidth: '55%',
                        borderRadius: 5
                    },
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    show: true,
                    width: 2,
                    colors: ['transparent']
                },
                xaxis: {
                    categories: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                },
                yaxis: {
                    title: {
                        text: 'ر.س'
                    }
                },
                fill: {
                    opacity: 1
                },
                tooltip: {
                    y: {
                        formatter: function (val) {
                            return val + " ر.س"
                        }
                    }
                },
                colors: ['#28a745', '#dc3545']
            };
            
            var cashflowChart = new ApexCharts(document.getElementById('cashflowChart'), cashflowOptions);
            cashflowChart.render();
            
            // مستمع حدث لحفظ الإيداع
            document.getElementById('saveDeposit').addEventListener('click', function() {
                var form = document.getElementById('depositForm');
                
                // التحقق من صحة النموذج
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }
                
                // جمع بيانات النموذج
                var depositData = {
                    amount: document.getElementById('depositAmount').value,
                    note: document.getElementById('depositNote').value
                };
                
                // هنا يمكن إضافة كود AJAX لإرسال البيانات إلى الخادم
                
                // إغلاق المودال
                var modal = bootstrap.Modal.getInstance(document.getElementById('depositModal'));
                modal.hide();
                
                // إعادة تحميل الصفحة أو تحديث البيانات
                alert('تم إيداع المبلغ بنجاح');
                // window.location.reload();
            });
            
            // مستمع حدث لحفظ السحب
            document.getElementById('saveWithdraw').addEventListener('click', function() {
                var form = document.getElementById('withdrawForm');
                
                // التحقق من صحة النموذج
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }
                
                // جمع بيانات النموذج
                var withdrawData = {
                    amount: document.getElementById('withdrawAmount').value,
                    reason: document.getElementById('withdrawReason').value,
                    note: document.getElementById('withdrawNote').value
                };
                
                // هنا يمكن إضافة كود AJAX لإرسال البيانات إلى الخادم
                
                // إغلاق المودال
                var modal = bootstrap.Modal.getInstance(document.getElementById('withdrawModal'));
                modal.hide();
                
                // إعادة تحميل الصفحة أو تحديث البيانات
                alert('تم سحب المبلغ بنجاح');
                // window.location.reload();
            });
        });
    </script>
</body>
</html>