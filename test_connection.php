<?php
// Include database connection
require_once 'db_connect.php';

// Test if connection is working
if (isset($conn) && !$conn->connect_error) {
    echo "<h2>Database Connection Test</h2>";
    echo "<p>Successfully connected to the MySQL server.</p>";
    echo "<p>Database: sqlagentahmed</p>";
    
    // Check if tables exist
    $tables = array("users", "sessions", "user_logs");
    echo "<h3>Tables:</h3>";
    echo "<ul>";
    
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            echo "<li>$table - <span style='color:green'>Exists</span></li>";
        } else {
            echo "<li>$table - <span style='color:red'>Does not exist</span></li>";
        }
    }
    
    echo "</ul>";
    
    // Close connection
    closeConnection($conn);
} else {
    echo "<h2>Database Connection Failed</h2>";
    echo "<p>Could not connect to the database. Please check your configuration.</p>";
}
?>