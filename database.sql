-- إن<PERSON>اء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS tec_store CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE tec_store;

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE,
    role ENUM('admin', 'manager', 'cashier') NOT NULL DEFAULT 'cashier',
    status TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول العملاء
CREATE TABLE IF NOT EXISTS customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHA<PERSON>(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(100),
    address TEXT,
    wallet_balance DECIMAL(10, 2) DEFAULT 0.00,
    debt_balance DECIMAL(10, 2) DEFAULT 0.00,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الموردين
CREATE TABLE IF NOT EXISTS suppliers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(100),
    address TEXT,
    balance DECIMAL(10, 2) DEFAULT 0.00,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الفئات
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول المنتجات
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    barcode VARCHAR(50) UNIQUE,
    category_id INT,
    purchase_price DECIMAL(10, 2) NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    stock INT NOT NULL DEFAULT 0,
    min_stock INT DEFAULT 0,
    image VARCHAR(255),
    description TEXT,
    status TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- جدول الفواتير
CREATE TABLE IF NOT EXISTS invoices (
    invoice_id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT,
    user_id INT NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    paid_amount DECIMAL(10, 2) NOT NULL,
    payment_type ENUM('cash', 'card', 'credit', 'wallet') NOT NULL,
    wallet_amount DECIMAL(10, 2) DEFAULT 0.00,
    change_amount DECIMAL(10, 2) DEFAULT 0.00,
    save_change TINYINT(1) DEFAULT 0,
    tax_amount DECIMAL(10, 2) DEFAULT 0.00,
    discount_amount DECIMAL(10, 2) DEFAULT 0.00,
    notes TEXT,
    status ENUM('completed', 'pending', 'canceled') NOT NULL DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- جدول تفاصيل الفواتير
CREATE TABLE IF NOT EXISTS invoice_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    total DECIMAL(10, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- جدول المشتريات
CREATE TABLE IF NOT EXISTS purchases (
    purchase_id INT AUTO_INCREMENT PRIMARY KEY,
    supplier_id INT,
    user_id INT NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    paid_amount DECIMAL(10, 2) NOT NULL,
    payment_type ENUM('cash', 'card', 'credit') NOT NULL,
    notes TEXT,
    status ENUM('completed', 'pending', 'canceled') NOT NULL DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- جدول تفاصيل المشتريات
CREATE TABLE IF NOT EXISTS purchase_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    purchase_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    total DECIMAL(10, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (purchase_id) REFERENCES purchases(purchase_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- جدول المصروفات
CREATE TABLE IF NOT EXISTS expenses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    category VARCHAR(50) NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- جدول الخزنة
CREATE TABLE IF NOT EXISTS cashbox (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('deposit', 'withdraw', 'sales', 'expense', 'purchase') NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    related_invoice INT,
    note TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- جدول معاملات العملاء
CREATE TABLE IF NOT EXISTS customer_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    invoice_id INT,
    type ENUM('invoice', 'payment', 'wallet_add', 'wallet_use', 'debt_add', 'debt_pay') NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_id) ON DELETE SET NULL
);

-- إدخال بيانات تجريبية

-- إدخال مستخدم افتراضي (كلمة المرور: admin123)
INSERT INTO users (username, password, name, email, role) VALUES
('admin', '$2y$10$8zTlsT8RyLhCYgCvjgzP5.ljPB5hjRvQQoL1Ey1.UTBfTgKPYjPGK', 'مدير النظام', '<EMAIL>', 'admin');

-- إدخال فئات المنتجات
INSERT INTO categories (name, description) VALUES
('إلكترونيات', 'أجهزة إلكترونية وملحقاتها'),
('ملابس', 'ملابس رجالية ونسائية'),
('أدوات منزلية', 'أدوات ومستلزمات منزلية');

-- إدخال منتجات تجريبية
INSERT INTO products (name, barcode, category_id, purchase_price, price, stock, min_stock, description) VALUES
('هاتف ذكي', '1234567890123', 1, 800.00, 1200.00, 10, 3, 'هاتف ذكي بمواصفات عالية'),
('لابتوب', '2345678901234', 1, 1500.00, 2000.00, 5, 2, 'لابتوب للاستخدام اليومي'),
('سماعات لاسلكية', '3456789012345', 1, 100.00, 150.00, 20, 5, 'سماعات لاسلكية عالية الجودة'),
('قميص رجالي', '4567890123456', 2, 50.00, 80.00, 30, 10, 'قميص رجالي قطن 100%'),
('بنطلون جينز', '5678901234567', 2, 70.00, 120.00, 25, 8, 'بنطلون جينز عالي الجودة'),
('طقم أواني', '6789012345678', 3, 200.00, 300.00, 8, 3, 'طقم أواني ستانلس ستيل'),
('مكنسة كهربائية', '7890123456789', 3, 300.00, 450.00, 6, 2, 'مكنسة كهربائية قوية');

-- إدخال عملاء تجريبيين
INSERT INTO customers (name, phone, email, address, wallet_balance, debt_balance, notes) VALUES
('أحمد محمد', '0512345678', '<EMAIL>', 'الرياض، حي النزهة', 100.00, 0.00, 'عميل منتظم'),
('محمد علي', '0523456789', '<EMAIL>', 'الرياض، حي الملز', 0.00, 500.00, 'يفضل الدفع الآجل'),
('فاطمة أحمد', '0534567890', '<EMAIL>', 'الرياض، حي الروضة', 50.00, 200.00, 'تفضل المنتجات الإلكترونية'),
('خالد عبدالله', '0545678901', '<EMAIL>', 'الرياض، حي السليمانية', 0.00, 0.00, 'عميل جديد');

-- إدخال موردين تجريبيين
INSERT INTO suppliers (name, phone, email, address, balance, notes) VALUES
('شركة الإلكترونيات الحديثة', '0512345678', '<EMAIL>', 'الرياض، المنطقة الصناعية', 0.00, 'مورد إلكترونيات'),
('مصنع الملابس الوطني', '0523456789', '<EMAIL>', 'جدة، المنطقة الصناعية', 1000.00, 'مورد ملابس'),
('شركة الأدوات المنزلية', '0534567890', '<EMAIL>', 'الدمام، المنطقة الصناعية', 500.00, 'مورد أدوات منزلية');

-- إدخال فواتير تجريبية
INSERT INTO invoices (customer_id, user_id, total_amount, paid_amount, payment_type, wallet_amount, tax_amount, discount_amount, notes) VALUES
(1, 1, 1380.00, 1380.00, 'cash', 0.00, 180.00, 0.00, 'فاتورة نقدية'),
(2, 1, 2300.00, 1800.00, 'credit', 0.00, 300.00, 0.00, 'فاتورة آجلة'),
(3, 1, 575.00, 525.00, 'wallet', 50.00, 75.00, 0.00, 'استخدام رصيد محفوظ'),
(1, 1, 345.00, 400.00, 'cash', 0.00, 45.00, 0.00, 'فاتورة مع فكة محفوظة');

-- إدخال تفاصيل الفواتير
INSERT INTO invoice_items (invoice_id, product_id, quantity, price, total) VALUES
(1, 3, 2, 150.00, 300.00),
(1, 4, 3, 80.00, 240.00),
(1, 6, 1, 300.00, 300.00),
(2, 2, 1, 2000.00, 2000.00),
(3, 3, 1, 150.00, 150.00),
(3, 4, 2, 80.00, 160.00),
(4, 3, 2, 150.00, 300.00);

-- إدخال معاملات العملاء
INSERT INTO customer_transactions (customer_id, invoice_id, type, amount, notes) VALUES
(1, 1, 'invoice', 1380.00, 'فاتورة مبيعات'),
(1, 1, 'payment', 1380.00, 'دفع نقدي'),
(2, 2, 'invoice', 2300.00, 'فاتورة مبيعات'),
(2, 2, 'payment', 1800.00, 'دفع جزئي'),
(2, 2, 'debt_add', 500.00, 'إضافة دين'),
(3, 3, 'invoice', 575.00, 'فاتورة مبيعات'),
(3, 3, 'payment', 525.00, 'دفع نقدي'),
(3, 3, 'wallet_use', 50.00, 'استخدام رصيد محفوظ'),
(1, 4, 'invoice', 345.00, 'فاتورة مبيعات'),
(1, 4, 'payment', 400.00, 'دفع نقدي'),
(1, 4, 'wallet_add', 55.00, 'إضافة رصيد محفوظ');

-- إدخال حركات الخزنة
INSERT INTO cashbox (user_id, type, amount, related_invoice, note) VALUES
(1, 'deposit', 5000.00, NULL, 'رصيد افتتاحي'),
(1, 'sales', 1380.00, 1, 'فاتورة مبيعات #1'),
(1, 'sales', 1800.00, 2, 'فاتورة مبيعات #2 (دفعة جزئية)'),
(1, 'sales', 525.00, 3, 'فاتورة مبيعات #3 (مع استخدام رصيد)'),
(1, 'sales', 400.00, 4, 'فاتورة مبيعات #4 (مع فكة محفوظة)'),
(1, 'expense', 200.00, NULL, 'مصروفات تشغيلية'),
(1, 'withdraw', 500.00, NULL, 'سحب للمصروفات اليومية');

-- إدخال مصروفات تجريبية
INSERT INTO expenses (user_id, category, amount, description) VALUES
(1, 'إيجار', 2000.00, 'إيجار المحل لشهر يناير'),
(1, 'رواتب', 3000.00, 'رواتب الموظفين لشهر يناير'),
(1, 'كهرباء', 500.00, 'فاتورة الكهرباء لشهر يناير'),
(1, 'مياه', 200.00, 'فاتورة المياه لشهر يناير'),
(1, 'إنترنت', 300.00, 'فاتورة الإنترنت لشهر يناير'),
(1, 'أخرى', 200.00, 'مصروفات متنوعة');