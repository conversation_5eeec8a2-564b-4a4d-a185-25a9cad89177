<?php
// بدء جلسة
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// استيراد ملف الاتصال بقاعدة البيانات
require_once 'config/db_connection.php';

// تهيئة المتغيرات الإحصائية
$total_expenses_count = 0;
$total_expenses_amount = 0;
$today_expenses_count = 0;
$today_expenses_amount = 0;
$month_expenses_count = 0;
$month_expenses_amount = 0;

// التحقق من وجود جدول المصروفات
$expenses_table_exists_query = "SHOW TABLES LIKE 'expenses'";
$expenses_table_exists_result = mysqli_query($conn, $expenses_table_exists_query);
$expenses_table_exists = mysqli_num_rows($expenses_table_exists_result) > 0;

// إذا كان جدول المصروفات موجودًا، نقوم بجلب الإحصائيات
if ($expenses_table_exists) {
    // إجمالي المصروفات
    $total_expenses_query = "SELECT COUNT(*) as count, SUM(amount) as total FROM expenses";
    $total_expenses_result = mysqli_query($conn, $total_expenses_query);
    if ($total_expenses_result && mysqli_num_rows($total_expenses_result) > 0) {
        $total_expenses_data = mysqli_fetch_assoc($total_expenses_result);
        $total_expenses_count = $total_expenses_data['count'] ?? 0;
        $total_expenses_amount = $total_expenses_data['total'] ?? 0;
    }
    
    // مصروفات اليوم
    $today_expenses_query = "SELECT COUNT(*) as count, SUM(amount) as total FROM expenses WHERE DATE(expense_date) = CURDATE()";
    $today_expenses_result = mysqli_query($conn, $today_expenses_query);
    if ($today_expenses_result && mysqli_num_rows($today_expenses_result) > 0) {
        $today_expenses_data = mysqli_fetch_assoc($today_expenses_result);
        $today_expenses_count = $today_expenses_data['count'] ?? 0;
        $today_expenses_amount = $today_expenses_data['total'] ?? 0;
    }
    
    // مصروفات الشهر
    $month_expenses_query = "SELECT COUNT(*) as count, SUM(amount) as total FROM expenses WHERE MONTH(expense_date) = MONTH(CURDATE()) AND YEAR(expense_date) = YEAR(CURDATE())";
    $month_expenses_result = mysqli_query($conn, $month_expenses_query);
    if ($month_expenses_result && mysqli_num_rows($month_expenses_result) > 0) {
        $month_expenses_data = mysqli_fetch_assoc($month_expenses_result);
        $month_expenses_count = $month_expenses_data['count'] ?? 0;
        $month_expenses_amount = $month_expenses_data['total'] ?? 0;
    }
}

// تهيئة مصفوفة الفئات
$categories = [];

// استعلام لجلب فئات المصروفات إذا كان الجدول موجودًا
if ($expenses_table_exists) {
    $categories_query = "SELECT DISTINCT category FROM expenses ORDER BY category";
    $categories_result = mysqli_query($conn, $categories_query);
    if ($categories_result) {
        while ($category = mysqli_fetch_assoc($categories_result)) {
            $categories[] = $category['category'];
        }
    }
}

// معالجة البحث والفلترة
$search = isset($_GET['search']) ? $_GET['search'] : '';
$category_filter = isset($_GET['category']) ? $_GET['category'] : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';

// تهيئة متغير نتيجة المصروفات
$expenses_result = false;

// بناء استعلام المصروفات إذا كان الجدول موجودًا
if ($expenses_table_exists) {
    $expenses_query = "SELECT * FROM expenses WHERE 1=1";
    
    if (!empty($search)) {
        $expenses_query .= " AND (description LIKE '%$search%' OR category LIKE '%$search%')";
    }
    
    if (!empty($category_filter)) {
        $expenses_query .= " AND category = '$category_filter'";
    }
    
    if (!empty($date_from)) {
        $expenses_query .= " AND expense_date >= '$date_from'";
    }
    
    if (!empty($date_to)) {
        $expenses_query .= " AND expense_date <= '$date_to'";
    }
    
    $expenses_query .= " ORDER BY expense_date DESC";
    $expenses_result = mysqli_query($conn, $expenses_query);
}

// معالجة إضافة مصروف جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_expense'])) {
    $description = mysqli_real_escape_string($conn, $_POST['description']);
    $amount = floatval($_POST['amount']);
    $category = mysqli_real_escape_string($conn, $_POST['category']);
    $expense_date = mysqli_real_escape_string($conn, $_POST['expense_date']);
    $payment_method = mysqli_real_escape_string($conn, $_POST['payment_method']);
    $notes = mysqli_real_escape_string($conn, $_POST['notes']);
    
    // التحقق من وجود جدول المصروفات
    if (!$expenses_table_exists) {
        // إنشاء جدول المصروفات إذا لم يكن موجودًا
        $create_expenses_table_query = "CREATE TABLE expenses (
            id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
            description VARCHAR(255) NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            category VARCHAR(100) NOT NULL,
            expense_date DATE NOT NULL,
            payment_method VARCHAR(50) NULL,
            notes TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        if (mysqli_query($conn, $create_expenses_table_query)) {
            $expenses_table_exists = true;
        } else {
            $error_message = "فشل في إنشاء جدول المصروفات: " . mysqli_error($conn);
        }
    }
    
    if ($expenses_table_exists) {
        // إدخال بيانات المصروف
        $insert_expense_query = "INSERT INTO expenses (description, amount, category, expense_date, payment_method, notes) 
                                VALUES ('$description', $amount, '$category', '$expense_date', '$payment_method', '$notes')";
        
        if (mysqli_query($conn, $insert_expense_query)) {
            $expense_id = mysqli_insert_id($conn);
            
            // التحقق من وجود جدول الخزنة
            $cashbox_table_exists_query = "SHOW TABLES LIKE 'cashbox'";
            $cashbox_table_exists_result = mysqli_query($conn, $cashbox_table_exists_query);
            $cashbox_table_exists = mysqli_num_rows($cashbox_table_exists_result) > 0;
            
            if (!$cashbox_table_exists) {
                // إنشاء جدول الخزنة إذا لم يكن موجودًا
                $create_cashbox_table_query = "CREATE TABLE cashbox (
                    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
                    transaction_type ENUM('income', 'expense') NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    transaction_date DATE NOT NULL,
                    description TEXT NULL,
                    reference_id INT(11) NULL,
                    reference_type VARCHAR(50) NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )";
                
                mysqli_query($conn, $create_cashbox_table_query);
                $cashbox_table_exists = true;
            }
            
            // تسجيل المصروف في الخزنة
            if ($cashbox_table_exists) {
                $cashbox_query = "INSERT INTO cashbox (transaction_type, amount, transaction_date, description, reference_id, reference_type) 
                                 VALUES ('expense', $amount, '$expense_date', '$description - $category', $expense_id, 'expense')";
                mysqli_query($conn, $cashbox_query);
            }
            
            $success_message = "تم إضافة المصروف بنجاح.";
            // إعادة تحميل الصفحة لتحديث البيانات
            header("Location: expenses.php?success=1");
            exit();
        } else {
            $error_message = "حدث خطأ أثناء إضافة المصروف: " . mysqli_error($conn);
        }
    }
}

// معالجة تعديل مصروف
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['edit_expense']) && $expenses_table_exists) {
    $expense_id = intval($_POST['expense_id']);
    $description = mysqli_real_escape_string($conn, $_POST['description']);
    $amount = floatval($_POST['amount']);
    $category = mysqli_real_escape_string($conn, $_POST['category']);
    $expense_date = mysqli_real_escape_string($conn, $_POST['expense_date']);
    $payment_method = mysqli_real_escape_string($conn, $_POST['payment_method']);
    $notes = mysqli_real_escape_string($conn, $_POST['notes']);
    
    // تحديث بيانات المصروف
    $update_expense_query = "UPDATE expenses SET 
                            description = '$description', 
                            amount = $amount, 
                            category = '$category', 
                            expense_date = '$expense_date', 
                            payment_method = '$payment_method', 
                            notes = '$notes' 
                            WHERE id = $expense_id";
    
    if (mysqli_query($conn, $update_expense_query)) {
        // التحقق من وجود جدول الخزنة
        $cashbox_table_exists_query = "SHOW TABLES LIKE 'cashbox'";
        $cashbox_table_exists_result = mysqli_query($conn, $cashbox_table_exists_query);
        $cashbox_table_exists = mysqli_num_rows($cashbox_table_exists_result) > 0;
        
        // تحديث سجل الخزنة إذا كان الجدول موجودًا
        if ($cashbox_table_exists) {
            $update_cashbox_query = "UPDATE cashbox SET 
                                    amount = $amount, 
                                    transaction_date = '$expense_date', 
                                    description = '$description - $category' 
                                    WHERE reference_id = $expense_id AND reference_type = 'expense'";
            mysqli_query($conn, $update_cashbox_query);
        }
        
        $success_message = "تم تحديث المصروف بنجاح.";
        // إعادة تحميل الصفحة لتحديث البيانات
        header("Location: expenses.php?success=2");
        exit();
    } else {
        $error_message = "حدث خطأ أثناء تحديث المصروف: " . mysqli_error($conn);
    }
}

// معالجة حذف مصروف
if (isset($_GET['delete']) && !empty($_GET['delete']) && $expenses_table_exists) {
    $expense_id = intval($_GET['delete']);
    
    // التحقق من وجود جدول الخزنة
    $cashbox_table_exists_query = "SHOW TABLES LIKE 'cashbox'";
    $cashbox_table_exists_result = mysqli_query($conn, $cashbox_table_exists_query);
    $cashbox_table_exists = mysqli_num_rows($cashbox_table_exists_result) > 0;
    
    // حذف سجل الخزنة المرتبط بالمصروف إذا كان الجدول موجودًا
    if ($cashbox_table_exists) {
        $delete_cashbox_query = "DELETE FROM cashbox WHERE reference_id = $expense_id AND reference_type = 'expense'";
        mysqli_query($conn, $delete_cashbox_query);
    }
    
    // حذف المصروف
    $delete_expense_query = "DELETE FROM expenses WHERE id = $expense_id";
    if (mysqli_query($conn, $delete_expense_query)) {
        $success_message = "تم حذف المصروف بنجاح.";
        // إعادة تحميل الصفحة لتحديث البيانات
        header("Location: expenses.php?success=3");
        exit();
    } else {
        $error_message = "حدث خطأ أثناء حذف المصروف: " . mysqli_error($conn);
    }
}

// رسائل النجاح
if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case 1:
            $success_message = "تم إضافة المصروف بنجاح.";
            break;
        case 2:
            $success_message = "تم تحديث المصروف بنجاح.";
            break;
        case 3:
            $success_message = "تم حذف المصروف بنجاح.";
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المصروفات - نظام إدارة المتجر</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <!-- ApexCharts -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --sidebar-width: 250px;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fb;
            color: #333;
            overflow-x: hidden;
        }
        
        /* تصميم الشريط الجانبي */
        .sidebar {
            height: 100vh;
            width: var(--sidebar-width);
            position: fixed;
            top: 0;
            right: 0;
            background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding-top: 20px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            z-index: 1000;
        }
        
        .sidebar .logo {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .logo h3 {
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 15px;
            border-radius: 8px;
            transition: all 0.3s;
            font-size: 16px;
        }
        
        .sidebar .nav-link:hover, 
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar .nav-link i {
            margin-left: 10px;
            width: 25px;
            text-align: center;
        }
        
        /* تصميم المحتوى الرئيسي */
        .main-content {
            margin-right: var(--sidebar-width);
            padding: 20px;
            transition: all 0.3s;
        }
        
        /* شريط التنقل العلوي */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        
        .search-bar {
            background-color: #f8f9fa;
            border-radius: 30px;
            padding: 8px 20px;
            border: 1px solid #eaeaea;
        }
        
        .search-bar input {
            background: transparent;
            border: none;
            width: 100%;
            padding: 5px 10px;
        }
        
        .search-bar input:focus {
            outline: none;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #f94144;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* بطاقات الإحصائيات */
        .stat-card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .stat-card .card-title {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-card .card-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .stat-card .card-footer {
            background: transparent;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 13px;
            padding: 8px 15px;
        }
        
        /* تصميم قسم المصروفات */
        .expenses-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .expense-filters {
            margin-bottom: 20px;
        }
        
        .expense-table .category-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            background-color: #f8f9fa;
            color: #333;
        }
        
        .expense-actions .btn {
            padding: 5px 10px;
            font-size: 14px;
        }
        
        /* تصميم الرسم البياني */
        .chart-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        /* تصميم النموذج */
        .modal-header {
            background-color: var(--primary-color);
            color: white;
        }
        
        .modal-footer {
            background-color: #f8f9fa;
        }
        
        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
                overflow: hidden;
            }
            
            .sidebar .logo h3, .sidebar .nav-link span {
                display: none;
            }
            
            .sidebar .nav-link {
                text-align: center;
                margin: 5px 10px;
            }
            
            .sidebar .nav-link i {
                margin-left: 0;
            }
            
            .main-content {
                margin-right: 70px;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-store"></i> متجرنا</h3>
        </div>
        <ul class="nav flex-column mt-4">
            <li class="nav-item">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales.php">
                    <i class="fas fa-shopping-cart"></i>
                    <span>المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales_management.php">
                    <i class="fas fa-chart-line"></i>
                    <span>إدارة المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="products.php">
                    <i class="fas fa-box"></i>
                    <span>المنتجات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customers.php">
                    <i class="fas fa-users"></i>
                    <span>العملاء</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="suppliers.php">
                    <i class="fas fa-truck"></i>
                    <span>الموردين</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="purchases.php">
                    <i class="fas fa-shopping-basket"></i>
                    <span>المشتريات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="expenses.php">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>المصروفات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="cashbox.php">
                    <i class="fas fa-cash-register"></i>
                    <span>الخزنة</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="financial.php">
                    <i class="fas fa-chart-pie"></i>
                    <span>الإدارة المالية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="barcode.php">
                    <i class="fas fa-barcode"></i>
                    <span>مولد الباركود</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="settings.php">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط التنقل العلوي -->
        <div class="top-navbar">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4 class="mb-0">إدارة المصروفات</h4>
                    <p class="mb-0 text-muted">مرحباً بعودتك، <?php echo $_SESSION['username']; ?>!</p>
                </div>
                <div class="col-md-6 d-flex justify-content-end">
                    <div class="d-flex align-items-center">
                        <div class="search-bar me-3">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="بحث...">
                        </div>
                        <div class="position-relative me-3">
                            <button class="btn btn-light rounded-circle">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-light rounded-circle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.php">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- عرض رسائل النجاح والخطأ -->
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <!-- بطاقات الإحصائيات -->
        <div class="row">
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-primary-subtle text-primary me-3">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div>
                            <div class="card-title">إجمالي المصروفات</div>
                            <div class="card-value"><?php echo number_format($total_expenses_amount, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-receipt"></i> <?php echo number_format($total_expenses_count); ?> مصروف
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-success-subtle text-success me-3">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <div>
                            <div class="card-title">مصروفات اليوم</div>
                            <div class="card-value"><?php echo number_format($today_expenses_amount, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-receipt"></i> <?php echo number_format($today_expenses_count); ?> مصروف
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-warning-subtle text-warning me-3">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div>
                            <div class="card-title">مصروفات الشهر</div>
                            <div class="card-value"><?php echo number_format($month_expenses_amount, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-receipt"></i> <?php echo number_format($month_expenses_count); ?> مصروف
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- الرسم البياني للمصروفات -->
            <div class="col-md-6">
                <div class="chart-container">
                    <h5 class="mb-4">توزيع المصروفات حسب الفئة</h5>
                    <div id="expensesByCategoryChart" style="height: 300px;"></div>
                </div>
            </div>
            
            <!-- الرسم البياني للمصروفات الشهرية -->
            <div class="col-md-6">
                <div class="chart-container">
                    <h5 class="mb-4">المصروفات الشهرية</h5>
                    <div id="monthlyExpensesChart" style="height: 300px;"></div>
                </div>
            </div>
        </div>

        <!-- قسم المصروفات -->
        <div class="expenses-container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0">قائمة المصروفات</h5>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addExpenseModal">
                        <i class="fas fa-plus me-1"></i> إضافة مصروف
                    </button>
                </div>
            </div>
            
            <!-- فلاتر البحث -->
            <div class="expense-filters">
                <form action="" method="get" class="row g-3">
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" name="search" placeholder="بحث بالوصف أو الفئة" value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="category">
                            <option value="">جميع الفئات</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category; ?>" <?php echo ($category_filter == $category) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" name="date_from" placeholder="من تاريخ" value="<?php echo $date_from; ?>">
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" name="date_to" placeholder="إلى تاريخ" value="<?php echo $date_to; ?>">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">بحث</button>
                    </div>
                </form>
            </div>
            
            <!-- جدول المصروفات -->
            <div class="expense-table">
                <div class="table-responsive">
                    <table class="table table-hover" id="expensesTable">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الوصف</th>
                                <th>المبلغ</th>
                                <th>الفئة</th>
                                <th>التاريخ</th>
                                <th>طريقة الدفع</th>
                                <th>ملاحظات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            if ($expenses_result && mysqli_num_rows($expenses_result) > 0) {
                                $counter = 1;
                                while ($expense = mysqli_fetch_assoc($expenses_result)) {
                                    echo '<tr>';
                                    echo '<td>' . $counter++ . '</td>';
                                    echo '<td>' . htmlspecialchars($expense['description']) . '</td>';
                                    echo '<td>' . number_format($expense['amount'], 2) . ' ر.س</td>';
                                    echo '<td><span class="category-badge">' . htmlspecialchars($expense['category']) . '</span></td>';
                                    echo '<td>' . date('Y-m-d', strtotime($expense['expense_date'])) . '</td>';
                                    echo '<td>' . htmlspecialchars(getPaymentMethodText($expense['payment_method'])) . '</td>';
                                    echo '<td>' . htmlspecialchars($expense['notes']) . '</td>';
                                    echo '<td class="expense-actions">';
                                    echo '<button class="btn btn-sm btn-info me-1" onclick="editExpense(' . $expense['id'] . ')" data-bs-toggle="modal" data-bs-target="#editExpenseModal"><i class="fas fa-edit"></i></button>';
                                    echo '<button class="btn btn-sm btn-danger" onclick="confirmDelete(' . $expense['id'] . ')"><i class="fas fa-trash"></i></button>';
                                    echo '</td>';
                                    echo '</tr>';
                                }
                            } else {
                                echo '<tr><td colspan="8" class="text-center">لا توجد مصروفات متاحة</td></tr>';
                            }
                            
                            // دالة لتحويل رمز طريقة الدفع إلى نص
                            function getPaymentMethodText($method) {
                                switch ($method) {
                                    case 'cash':
                                        return 'نقدي';
                                    case 'bank_transfer':
                                        return 'تحويل بنكي';
                                    case 'check':
                                        return 'شيك';
                                    case 'credit_card':
                                        return 'بطاقة ائتمان';
                                    default:
                                        return $method;
                                }
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال إضافة مصروف جديد -->
    <div class="modal fade" id="addExpenseModal" tabindex="-1" aria-labelledby="addExpenseModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addExpenseModalLabel">إضافة مصروف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addExpenseForm" action="" method="post">
                        <input type="hidden" name="add_expense" value="1">
                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="description" name="description" required>
                        </div>
                        <div class="mb-3">
                            <label for="amount" class="form-label">المبلغ <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0" required>
                        </div>
                        <div class="mb-3">
                            <label for="category" class="form-label">الفئة <span class="text-danger">*</span></label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">اختر الفئة</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category; ?>"><?php echo htmlspecialchars($category); ?></option>
                                <?php endforeach; ?>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="mb-3" id="otherCategoryDiv" style="display: none;">
                            <label for="otherCategory" class="form-label">فئة أخرى <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="otherCategory" name="otherCategory">
                        </div>
                        <div class="mb-3">
                            <label for="expense_date" class="form-label">تاريخ المصروف <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="expense_date" name="expense_date" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="payment_method" class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                            <select class="form-select" id="payment_method" name="payment_method" required>
                                <option value="cash">نقدي</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                                <option value="credit_card">بطاقة ائتمان</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="addExpenseForm" class="btn btn-primary">إضافة المصروف</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال تعديل مصروف -->
    <div class="modal fade" id="editExpenseModal" tabindex="-1" aria-labelledby="editExpenseModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editExpenseModalLabel">تعديل مصروف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editExpenseForm" action="" method="post">
                        <input type="hidden" name="edit_expense" value="1">
                        <input type="hidden" name="expense_id" id="edit_expense_id">
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">الوصف <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_description" name="description" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_amount" class="form-label">المبلغ <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="edit_amount" name="amount" step="0.01" min="0" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_category" class="form-label">الفئة <span class="text-danger">*</span></label>
                            <select class="form-select" id="edit_category" name="category" required>
                                <option value="">اختر الفئة</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category; ?>"><?php echo htmlspecialchars($category); ?></option>
                                <?php endforeach; ?>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="mb-3" id="editOtherCategoryDiv" style="display: none;">
                            <label for="editOtherCategory" class="form-label">فئة أخرى <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editOtherCategory" name="editOtherCategory">
                        </div>
                        <div class="mb-3">
                            <label for="edit_expense_date" class="form-label">تاريخ المصروف <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="edit_expense_date" name="expense_date" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_payment_method" class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                            <select class="form-select" id="edit_payment_method" name="payment_method" required>
                                <option value="cash">نقدي</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                                <option value="credit_card">بطاقة ائتمان</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="edit_notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="edit_notes" name="notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="editExpenseForm" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        // تهيئة جدول المصروفات
        $(document).ready(function() {
            $('#expensesTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
                },
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "الكل"]]
            });
            
            // إظهار حقل "فئة أخرى" عند اختيار "أخرى" من قائمة الفئات
            $('#category').on('change', function() {
                if ($(this).val() === 'other') {
                    $('#otherCategoryDiv').show();
                    $('#otherCategory').prop('required', true);
                } else {
                    $('#otherCategoryDiv').hide();
                    $('#otherCategory').prop('required', false);
                }
            });
            
            $('#edit_category').on('change', function() {
                if ($(this).val() === 'other') {
                    $('#editOtherCategoryDiv').show();
                    $('#editOtherCategory').prop('required', true);
                } else {
                    $('#editOtherCategoryDiv').hide();
                    $('#editOtherCategory').prop('required', false);
                }
            });
            
            // معالجة إرسال نموذج إضافة مصروف
            $('#addExpenseForm').on('submit', function(e) {
                if ($('#category').val() === 'other' && $('#otherCategory').val().trim() === '') {
                    e.preventDefault();
                    alert('يرجى إدخال الفئة الأخرى');
                    return false;
                }
                
                if ($('#category').val() === 'other') {
                    $('#category').val($('#otherCategory').val().trim());
                }
            });
            
            // معالجة إرسال نموذج تعديل مصروف
            $('#editExpenseForm').on('submit', function(e) {
                if ($('#edit_category').val() === 'other' && $('#editOtherCategory').val().trim() === '') {
                    e.preventDefault();
                    alert('يرجى إدخال الفئة الأخرى');
                    return false;
                }
                
                if ($('#edit_category').val() === 'other') {
                    $('#edit_category').val($('#editOtherCategory').val().trim());
                }
            });
            
            // رسم البيانات
            renderCharts();
        });
        
        // دالة لتعديل المصروف
        function editExpense(expenseId) {
            // هنا يمكن إضافة كود AJAX لجلب بيانات المصروف من الخادم
            // لأغراض العرض، سنستخدم بيانات وهمية
            
            // في التطبيق الحقيقي، يجب استبدال هذا بطلب AJAX
            fetch('get_expense.php?id=' + expenseId)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('edit_expense_id').value = data.id;
                    document.getElementById('edit_description').value = data.description;
                    document.getElementById('edit_amount').value = data.amount;
                    
                    // التحقق مما إذا كانت الفئة موجودة في القائمة
                    const categorySelect = document.getElementById('edit_category');
                    let categoryFound = false;
                    
                    for (let i = 0; i < categorySelect.options.length; i++) {
                        if (categorySelect.options[i].value === data.category) {
                            categorySelect.selectedIndex = i;
                            categoryFound = true;
                            break;
                        }
                    }
                    
                    if (!categoryFound && data.category) {
                        // إذا لم تكن الفئة موجودة، اختر "أخرى" وأضف الفئة في حقل "فئة أخرى"
                        for (let i = 0; i < categorySelect.options.length; i++) {
                            if (categorySelect.options[i].value === 'other') {
                                categorySelect.selectedIndex = i;
                                document.getElementById('editOtherCategoryDiv').style.display = 'block';
                                document.getElementById('editOtherCategory').value = data.category;
                                document.getElementById('editOtherCategory').required = true;
                                break;
                            }
                        }
                    }
                    
                    document.getElementById('edit_expense_date').value = data.expense_date.split(' ')[0];
                    document.getElementById('edit_payment_method').value = data.payment_method;
                    document.getElementById('edit_notes').value = data.notes;
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء جلب بيانات المصروف');
                });
        }
        
        // دالة لتأكيد حذف المصروف
        function confirmDelete(expenseId) {
            if (confirm('هل أنت متأكد من رغبتك في حذف هذا المصروف؟')) {
                window.location.href = 'expenses.php?delete=' + expenseId;
            }
        }
        
        // دالة لرسم البيانات
        function renderCharts() {
            // بيانات الرسم البياني للمصروفات حسب الفئة
            // في التطبيق الحقيقي، يجب استبدال هذا ببيانات حقيقية من الخادم
            const expensesByCategoryData = [
                <?php
                // استعلام لجلب المصروفات حسب الفئة
                $expenses_by_category_query = "SELECT category, SUM(amount) as total FROM expenses GROUP BY category ORDER BY total DESC";
                $expenses_by_category_result = mysqli_query($conn, $expenses_by_category_query);
                
                while ($category_data = mysqli_fetch_assoc($expenses_by_category_result)) {
                    echo '{';
                    echo 'category: "' . addslashes($category_data['category']) . '",';
                    echo 'amount: ' . $category_data['total'];
                    echo '},';
                }
                ?>
            ];
            
            // بيانات الرسم البياني للمصروفات الشهرية
            // في التطبيق الحقيقي، يجب استبدال هذا ببيانات حقيقية من الخادم
            const monthlyExpensesData = [
                <?php
                // استعلام لجلب المصروفات الشهرية
                $monthly_expenses_query = "SELECT 
                                          DATE_FORMAT(expense_date, '%Y-%m') as month,
                                          SUM(amount) as total 
                                          FROM expenses 
                                          WHERE expense_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH) 
                                          GROUP BY month 
                                          ORDER BY month";
                $monthly_expenses_result = mysqli_query($conn, $monthly_expenses_query);
                
                while ($monthly_data = mysqli_fetch_assoc($monthly_expenses_result)) {
                    echo '{';
                    echo 'month: "' . $monthly_data['month'] . '",';
                    echo 'amount: ' . $monthly_data['total'];
                    echo '},';
                }
                ?>
            ];
            
            // رسم البياني للمصروفات حسب الفئة
            const expensesByCategoryOptions = {
                series: expensesByCategoryData.map(item => item.amount),
                chart: {
                    type: 'pie',
                    height: 300,
                    fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
                },
                labels: expensesByCategoryData.map(item => item.category),
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            width: 200
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }],
                colors: ['#4361ee', '#3f37c9', '#4cc9f0', '#ff9e00', '#f94144', '#4895ef', '#8338ec', '#fb5607', '#ffbe0b', '#3a86ff'],
                tooltip: {
                    y: {
                        formatter: function(value) {
                            return value.toFixed(2) + ' ر.س';
                        }
                    }
                }
            };
            
            const expensesByCategoryChart = new ApexCharts(document.querySelector("#expensesByCategoryChart"), expensesByCategoryOptions);
            expensesByCategoryChart.render();
            
            // رسم البياني للمصروفات الشهرية
            const monthlyExpensesOptions = {
                series: [{
                    name: 'المصروفات',
                    data: monthlyExpensesData.map(item => item.amount)
                }],
                chart: {
                    type: 'bar',
                    height: 300,
                    fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
                    toolbar: {
                        show: false
                    }
                },
                plotOptions: {
                    bar: {
                        horizontal: false,
                        columnWidth: '55%',
                        endingShape: 'rounded'
                    },
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    show: true,
                    width: 2,
                    colors: ['transparent']
                },
                xaxis: {
                    categories: monthlyExpensesData.map(item => {
                        const [year, month] = item.month.split('-');
                        const date = new Date(year, month - 1);
                        return date.toLocaleDateString('ar-SA', { month: 'short', year: 'numeric' });
                    }),
                },
                yaxis: {
                    title: {
                        text: 'ر.س'
                    }
                },
                fill: {
                    opacity: 1
                },
                tooltip: {
                    y: {
                        formatter: function(value) {
                            return value.toFixed(2) + ' ر.س';
                        }
                    }
                },
                colors: ['#4361ee']
            };
            
            const monthlyExpensesChart = new ApexCharts(document.querySelector("#monthlyExpensesChart"), monthlyExpensesOptions);
            monthlyExpensesChart.render();
        }
    </script>
</body>
</html>