<?php
// بدء جلسة
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// استيراد ملف الاتصال بقاعدة البيانات
require_once 'config/db_connection.php';

// استعلام لجلب إحصائيات المنتجات
$total_products_query = "SELECT COUNT(*) as total FROM products";
$total_products_result = mysqli_query($conn, $total_products_query);
$total_products = mysqli_fetch_assoc($total_products_result)['total'] ?? 0;

$low_stock_query = "SELECT COUNT(*) as total FROM products WHERE stock <= min_stock AND stock > 0";
$low_stock_result = mysqli_query($conn, $low_stock_query);
$low_stock = mysqli_fetch_assoc($low_stock_result)['total'] ?? 0;

$out_of_stock_query = "SELECT COUNT(*) as total FROM products WHERE stock = 0";
$out_of_stock_result = mysqli_query($conn, $out_of_stock_query);
$out_of_stock = mysqli_fetch_assoc($out_of_stock_result)['total'] ?? 0;

$total_inventory_value_query = "SELECT SUM(stock * purchase_price) as total FROM products";
$total_inventory_value_result = mysqli_query($conn, $total_inventory_value_query);
$total_inventory_value = mysqli_fetch_assoc($total_inventory_value_result)['total'] ?? 0;

// استعلام لجلب الفئات
$categories_query = "SELECT * FROM categories ORDER BY name";
$categories_result = mysqli_query($conn, $categories_query);
$categories = [];
while ($category = mysqli_fetch_assoc($categories_result)) {
    $categories[] = $category;
}

// معالجة البحث والفلترة
$search = isset($_GET['search']) ? $_GET['search'] : '';
$category_filter = isset($_GET['category']) ? $_GET['category'] : '';
$stock_filter = isset($_GET['stock']) ? $_GET['stock'] : '';

// بناء استعلام المنتجات
$products_query = "SELECT p.*, c.name as category_name 
                  FROM products p 
                  LEFT JOIN categories c ON p.category_id = c.id 
                  WHERE 1=1";

if (!empty($search)) {
    $products_query .= " AND (p.name LIKE '%$search%' OR p.barcode LIKE '%$search%')";
}

if (!empty($category_filter)) {
    $products_query .= " AND p.category_id = $category_filter";
}

if ($stock_filter == 'low') {
    $products_query .= " AND p.stock <= p.min_stock AND p.stock > 0";
} elseif ($stock_filter == 'out') {
    $products_query .= " AND p.stock = 0";
}

$products_query .= " ORDER BY p.name";
$products_result = mysqli_query($conn, $products_query);

// معالجة إضافة منتج جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_product'])) {
    $name = mysqli_real_escape_string($conn, $_POST['name']);
    $barcode = !empty($_POST['barcode']) ? mysqli_real_escape_string($conn, $_POST['barcode']) : null;
    $category_id = !empty($_POST['category_id']) ? intval($_POST['category_id']) : null;
    $purchase_price = floatval($_POST['purchase_price']);
    $price = floatval($_POST['price']);
    $stock = intval($_POST['stock']);
    $min_stock = intval($_POST['min_stock']);
    $description = mysqli_real_escape_string($conn, $_POST['description']);
    
    // التحقق من عدم تكرار الباركود
    if (!empty($barcode)) {
        $check_barcode_query = "SELECT id FROM products WHERE barcode = '$barcode'";
        $check_barcode_result = mysqli_query($conn, $check_barcode_query);
        if (mysqli_num_rows($check_barcode_result) > 0) {
            $error_message = "الباركود مستخدم بالفعل، يرجى استخدام باركود آخر.";
        }
    }
    
    if (!isset($error_message)) {
        $insert_query = "INSERT INTO products (name, barcode, category_id, purchase_price, price, stock, min_stock, description) 
                        VALUES ('$name', " . ($barcode ? "'$barcode'" : "NULL") . ", " . ($category_id ? "$category_id" : "NULL") . ", 
                        $purchase_price, $price, $stock, $min_stock, '$description')";
        
        if (mysqli_query($conn, $insert_query)) {
            $success_message = "تم إضافة المنتج بنجاح.";
            // إعادة تحميل الصفحة لتحديث البيانات
            header("Location: products.php?success=1");
            exit();
        } else {
            $error_message = "حدث خطأ أثناء إضافة المنتج: " . mysqli_error($conn);
        }
    }
}

// معالجة تعديل منتج
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['edit_product'])) {
    $product_id = intval($_POST['product_id']);
    $name = mysqli_real_escape_string($conn, $_POST['name']);
    $barcode = !empty($_POST['barcode']) ? mysqli_real_escape_string($conn, $_POST['barcode']) : null;
    $category_id = !empty($_POST['category_id']) ? intval($_POST['category_id']) : null;
    $purchase_price = floatval($_POST['purchase_price']);
    $price = floatval($_POST['price']);
    $stock = intval($_POST['stock']);
    $min_stock = intval($_POST['min_stock']);
    $description = mysqli_real_escape_string($conn, $_POST['description']);
    
    // التحقق من عدم تكرار الباركود
    if (!empty($barcode)) {
        $check_barcode_query = "SELECT id FROM products WHERE barcode = '$barcode' AND id != $product_id";
        $check_barcode_result = mysqli_query($conn, $check_barcode_query);
        if (mysqli_num_rows($check_barcode_result) > 0) {
            $error_message = "الباركود مستخدم بالفعل، يرجى استخدام باركود آخر.";
        }
    }
    
    if (!isset($error_message)) {
        $update_query = "UPDATE products SET 
                        name = '$name', 
                        barcode = " . ($barcode ? "'$barcode'" : "NULL") . ", 
                        category_id = " . ($category_id ? "$category_id" : "NULL") . ", 
                        purchase_price = $purchase_price, 
                        price = $price, 
                        stock = $stock, 
                        min_stock = $min_stock, 
                        description = '$description' 
                        WHERE id = $product_id";
        
        if (mysqli_query($conn, $update_query)) {
            $success_message = "تم تحديث المنتج بنجاح.";
            // إعادة تحميل الصفحة لتحديث البيانات
            header("Location: products.php?success=2");
            exit();
        } else {
            $error_message = "حدث خطأ أثناء تحديث المنتج: " . mysqli_error($conn);
        }
    }
}

// معالجة حذف منتج
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $product_id = intval($_GET['delete']);
    
    // التحقق من عدم وجود مبيعات مرتبطة بالمنتج
    $check_sales_query = "SELECT COUNT(*) as count FROM invoice_items WHERE product_id = $product_id";
    $check_sales_result = mysqli_query($conn, $check_sales_query);
    $sales_count = mysqli_fetch_assoc($check_sales_result)['count'];
    
    if ($sales_count > 0) {
        $error_message = "لا يمكن حذف المنتج لأنه مرتبط بمبيعات سابقة.";
    } else {
        $delete_query = "DELETE FROM products WHERE id = $product_id";
        if (mysqli_query($conn, $delete_query)) {
            $success_message = "تم حذف المنتج بنجاح.";
            // إعادة تحميل الصفحة لتحديث البيانات
            header("Location: products.php?success=3");
            exit();
        } else {
            $error_message = "حدث خطأ أثناء حذف المنتج: " . mysqli_error($conn);
        }
    }
}

// معالجة إضافة فئة جديدة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_category'])) {
    $category_name = mysqli_real_escape_string($conn, $_POST['category_name']);
    $category_description = mysqli_real_escape_string($conn, $_POST['category_description']);
    
    $insert_category_query = "INSERT INTO categories (name, description) VALUES ('$category_name', '$category_description')";
    
    if (mysqli_query($conn, $insert_category_query)) {
        $success_message = "تم إضافة الفئة بنجاح.";
        // إعادة تحميل الصفحة لتحديث البيانات
        header("Location: products.php?success=4");
        exit();
    } else {
        $error_message = "حدث خطأ أثناء إضافة الفئة: " . mysqli_error($conn);
    }
}

// معالجة تعديل كمية المخزون
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_stock'])) {
    $product_id = intval($_POST['product_id']);
    $new_stock = intval($_POST['new_stock']);
    $stock_note = mysqli_real_escape_string($conn, $_POST['stock_note']);
    
    $update_stock_query = "UPDATE products SET stock = $new_stock WHERE id = $product_id";
    
    if (mysqli_query($conn, $update_stock_query)) {
        $success_message = "تم تحديث المخزون بنجاح.";
        // إعادة تحميل الصفحة لتحديث البيانات
        header("Location: products.php?success=5");
        exit();
    } else {
        $error_message = "حدث خطأ أثناء تحديث المخزون: " . mysqli_error($conn);
    }
}

// رسائل النجاح
if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case 1:
            $success_message = "تم إضافة المنتج بنجاح.";
            break;
        case 2:
            $success_message = "تم تحديث المنتج بنجاح.";
            break;
        case 3:
            $success_message = "تم حذف المنتج بنجاح.";
            break;
        case 4:
            $success_message = "تم إضافة الفئة بنجاح.";
            break;
        case 5:
            $success_message = "تم تحديث المخزون بنجاح.";
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - نظام إدارة المتجر</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --sidebar-width: 250px;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fb;
            color: #333;
            overflow-x: hidden;
        }
        
        /* تصميم الشريط الجانبي */
        .sidebar {
            height: 100vh;
            width: var(--sidebar-width);
            position: fixed;
            top: 0;
            right: 0;
            background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding-top: 20px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            z-index: 1000;
        }
        
        .sidebar .logo {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .logo h3 {
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 15px;
            border-radius: 8px;
            transition: all 0.3s;
            font-size: 16px;
        }
        
        .sidebar .nav-link:hover, 
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar .nav-link i {
            margin-left: 10px;
            width: 25px;
            text-align: center;
        }
        
        /* تصميم المحتوى الرئيسي */
        .main-content {
            margin-right: var(--sidebar-width);
            padding: 20px;
            transition: all 0.3s;
        }
        
        /* شريط التنقل العلوي */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        
        .search-bar {
            background-color: #f8f9fa;
            border-radius: 30px;
            padding: 8px 20px;
            border: 1px solid #eaeaea;
        }
        
        .search-bar input {
            background: transparent;
            border: none;
            width: 100%;
            padding: 5px 10px;
        }
        
        .search-bar input:focus {
            outline: none;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #f94144;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* بطاقات الإحصائيات */
        .stat-card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .stat-card .card-title {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-card .card-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .stat-card .card-footer {
            background: transparent;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 13px;
            padding: 8px 15px;
        }
        
        /* تصميم قسم المنتجات */
        .products-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .product-filters {
            margin-bottom: 20px;
        }
        
        .product-table img {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .product-table .stock-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .product-table .stock-status.in-stock {
            background-color: #e6f4ea;
            color: #0f9d58;
        }
        
        .product-table .stock-status.low-stock {
            background-color: #fef7e0;
            color: #f9ab00;
        }
        
        .product-table .stock-status.out-of-stock {
            background-color: #fce8e6;
            color: #ea4335;
        }
        
        .product-actions .btn {
            padding: 5px 10px;
            font-size: 14px;
        }
        
        /* تصميم النموذج */
        .modal-header {
            background-color: var(--primary-color);
            color: white;
        }
        
        .modal-footer {
            background-color: #f8f9fa;
        }
        
        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
                overflow: hidden;
            }
            
            .sidebar .logo h3, .sidebar .nav-link span {
                display: none;
            }
            
            .sidebar .nav-link {
                text-align: center;
                margin: 5px 10px;
            }
            
            .sidebar .nav-link i {
                margin-left: 0;
            }
            
            .main-content {
                margin-right: 70px;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-store"></i> متجرنا</h3>
        </div>
        <ul class="nav flex-column mt-4">
            <li class="nav-item">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales.php">
                    <i class="fas fa-shopping-cart"></i>
                    <span>المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales_management.php">
                    <i class="fas fa-chart-line"></i>
                    <span>إدارة المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="products.php">
                    <i class="fas fa-box"></i>
                    <span>المنتجات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customers.php">
                    <i class="fas fa-users"></i>
                    <span>العملاء</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="suppliers.php">
                    <i class="fas fa-truck"></i>
                    <span>الموردين</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="purchases.php">
                    <i class="fas fa-shopping-basket"></i>
                    <span>المشتريات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="expenses.php">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>المصروفات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="cashbox.php">
                    <i class="fas fa-cash-register"></i>
                    <span>الخزنة</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="financial.php">
                    <i class="fas fa-chart-pie"></i>
                    <span>الإدارة المالية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="barcode.php">
                    <i class="fas fa-barcode"></i>
                    <span>مولد الباركود</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="settings.php">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط التنقل العلوي -->
        <div class="top-navbar">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4 class="mb-0">إدارة المنتجات</h4>
                    <p class="mb-0 text-muted">مرحباً بعودتك، <?php echo $_SESSION['username']; ?>!</p>
                </div>
                <div class="col-md-6 d-flex justify-content-end">
                    <div class="d-flex align-items-center">
                        <div class="search-bar me-3">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="بحث...">
                        </div>
                        <div class="position-relative me-3">
                            <button class="btn btn-light rounded-circle">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-light rounded-circle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.php">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- عرض رسائل النجاح والخطأ -->
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <!-- بطاقات الإحصائيات -->
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-primary-subtle text-primary me-3">
                            <i class="fas fa-box"></i>
                        </div>
                        <div>
                            <div class="card-title">إجمالي المنتجات</div>
                            <div class="card-value"><?php echo number_format($total_products); ?></div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-sync-alt"></i> آخر تحديث: <?php echo date('Y-m-d H:i'); ?>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-warning-subtle text-warning me-3">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div>
                            <div class="card-title">منتجات قاربت على النفاذ</div>
                            <div class="card-value"><?php echo number_format($low_stock); ?></div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-info-circle"></i> تحتاج إلى إعادة الطلب
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-danger-subtle text-danger me-3">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div>
                            <div class="card-title">منتجات نفذت</div>
                            <div class="card-value"><?php echo number_format($out_of_stock); ?></div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-exclamation-circle"></i> تحتاج إلى طلب فوري
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-success-subtle text-success me-3">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div>
                            <div class="card-title">قيمة المخزون</div>
                            <div class="card-value"><?php echo number_format($total_inventory_value, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-calculator"></i> بسعر التكلفة
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم المنتجات -->
        <div class="products-container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0">قائمة المنتجات</h5>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#addProductModal">
                        <i class="fas fa-plus me-1"></i> إضافة منتج
                    </button>
                    <button class="btn btn-outline-primary me-2" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                        <i class="fas fa-folder-plus me-1"></i> إضافة فئة
                    </button>
                    <a href="barcode.php" class="btn btn-outline-secondary">
                        <i class="fas fa-barcode me-1"></i> طباعة الباركود
                    </a>
                </div>
            </div>
            
            <!-- فلاتر البحث -->
            <div class="product-filters">
                <form action="" method="get" class="row g-3">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" name="search" placeholder="بحث بالاسم أو الباركود" value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="category">
                            <option value="">جميع الفئات</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>" <?php echo ($category_filter == $category['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="stock">
                            <option value="">جميع المنتجات</option>
                            <option value="low" <?php echo ($stock_filter == 'low') ? 'selected' : ''; ?>>قاربت على النفاذ</option>
                            <option value="out" <?php echo ($stock_filter == 'out') ? 'selected' : ''; ?>>نفذت</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">تطبيق</button>
                    </div>
                </form>
            </div>
            
            <!-- جدول المنتجات -->
            <div class="product-table">
                <div class="table-responsive">
                    <table class="table table-hover" id="productsTable">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الصورة</th>
                                <th>الاسم</th>
                                <th>الباركود</th>
                                <th>الفئة</th>
                                <th>سعر الشراء</th>
                                <th>سعر البيع</th>
                                <th>المخزون</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            if (mysqli_num_rows($products_result) > 0) {
                                $counter = 1;
                                while ($product = mysqli_fetch_assoc($products_result)) {
                                    // تحديد حالة المخزون
                                    $stock_status = '';
                                    $stock_class = '';
                                    
                                    if ($product['stock'] <= 0) {
                                        $stock_status = 'نفذت';
                                        $stock_class = 'out-of-stock';
                                    } elseif ($product['stock'] <= $product['min_stock']) {
                                        $stock_status = 'قاربت على النفاذ';
                                        $stock_class = 'low-stock';
                                    } else {
                                        $stock_status = 'متوفر';
                                        $stock_class = 'in-stock';
                                    }
                                    
                                    echo '<tr>';
                                    echo '<td>' . $counter++ . '</td>';
                                    echo '<td>';
                                    if (!empty($product['image'])) {
                                        echo '<img src="' . $product['image'] . '" alt="' . $product['name'] . '">';
                                    } else {
                                        echo '<img src="https://via.placeholder.com/50" alt="صورة افتراضية">';
                                    }
                                    echo '</td>';
                                    echo '<td>' . htmlspecialchars($product['name']) . '</td>';
                                    echo '<td>' . (!empty($product['barcode']) ? $product['barcode'] : '-') . '</td>';
                                    echo '<td>' . (!empty($product['category_name']) ? htmlspecialchars($product['category_name']) : '-') . '</td>';
                                    echo '<td>' . number_format($product['purchase_price'], 2) . ' ر.س</td>';
                                    echo '<td>' . number_format($product['price'], 2) . ' ر.س</td>';
                                    echo '<td>' . $product['stock'] . '</td>';
                                    echo '<td><span class="stock-status ' . $stock_class . '">' . $stock_status . '</span></td>';
                                    echo '<td class="product-actions">';
                                    echo '<button class="btn btn-sm btn-info me-1" onclick="editProduct(' . $product['id'] . ')" data-bs-toggle="modal" data-bs-target="#editProductModal"><i class="fas fa-edit"></i></button>';
                                    echo '<button class="btn btn-sm btn-success me-1" onclick="updateStock(' . $product['id'] . ')" data-bs-toggle="modal" data-bs-target="#updateStockModal"><i class="fas fa-cubes"></i></button>';
                                    echo '<button class="btn btn-sm btn-danger" onclick="confirmDelete(' . $product['id'] . ')"><i class="fas fa-trash"></i></button>';
                                    echo '</td>';
                                    echo '</tr>';
                                }
                            } else {
                                echo '<tr><td colspan="10" class="text-center">لا توجد منتجات متاحة</td></tr>';
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال إضافة منتج جديد -->
    <div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addProductModalLabel">إضافة منتج جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addProductForm" action="" method="post">
                        <input type="hidden" name="add_product" value="1">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">اسم المنتج <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-6">
                                <label for="barcode" class="form-label">الباركود</label>
                                <input type="text" class="form-control" id="barcode" name="barcode">
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="category_id" class="form-label">الفئة</label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="">اختر الفئة</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="purchase_price" class="form-label">سعر الشراء <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="purchase_price" name="purchase_price" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="price" class="form-label">سعر البيع <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-6">
                                <label for="stock" class="form-label">الكمية المتوفرة <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="stock" name="stock" min="0" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="min_stock" class="form-label">الحد الأدنى للمخزون</label>
                                <input type="number" class="form-control" id="min_stock" name="min_stock" min="0" value="0">
                            </div>
                            <div class="col-md-6">
                                <label for="image" class="form-label">صورة المنتج</label>
                                <input type="file" class="form-control" id="image" name="image">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">وصف المنتج</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="addProductForm" class="btn btn-primary">إضافة المنتج</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال تعديل منتج -->
    <div class="modal fade" id="editProductModal" tabindex="-1" aria-labelledby="editProductModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editProductModalLabel">تعديل منتج</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editProductForm" action="" method="post">
                        <input type="hidden" name="edit_product" value="1">
                        <input type="hidden" name="product_id" id="edit_product_id">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_name" class="form-label">اسم المنتج <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_name" name="name" required>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_barcode" class="form-label">الباركود</label>
                                <input type="text" class="form-control" id="edit_barcode" name="barcode">
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_category_id" class="form-label">الفئة</label>
                                <select class="form-select" id="edit_category_id" name="category_id">
                                    <option value="">اختر الفئة</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_purchase_price" class="form-label">سعر الشراء <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="edit_purchase_price" name="purchase_price" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_price" class="form-label">سعر البيع <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="edit_price" name="price" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_stock" class="form-label">الكمية المتوفرة <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="edit_stock" name="stock" min="0" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_min_stock" class="form-label">الحد الأدنى للمخزون</label>
                                <input type="number" class="form-control" id="edit_min_stock" name="min_stock" min="0">
                            </div>
                            <div class="col-md-6">
                                <label for="edit_image" class="form-label">صورة المنتج</label>
                                <input type="file" class="form-control" id="edit_image" name="image">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">وصف المنتج</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="editProductForm" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال تحديث المخزون -->
    <div class="modal fade" id="updateStockModal" tabindex="-1" aria-labelledby="updateStockModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="updateStockModalLabel">تحديث المخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="updateStockForm" action="" method="post">
                        <input type="hidden" name="update_stock" value="1">
                        <input type="hidden" name="product_id" id="stock_product_id">
                        <div class="mb-3">
                            <label for="product_name" class="form-label">اسم المنتج</label>
                            <input type="text" class="form-control" id="product_name" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="current_stock" class="form-label">المخزون الحالي</label>
                            <input type="text" class="form-control" id="current_stock" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="new_stock" class="form-label">المخزون الجديد <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="new_stock" name="new_stock" min="0" required>
                        </div>
                        <div class="mb-3">
                            <label for="stock_note" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="stock_note" name="stock_note" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="updateStockForm" class="btn btn-primary">تحديث المخزون</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال إضافة فئة جديدة -->
    <div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCategoryModalLabel">إضافة فئة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addCategoryForm" action="" method="post">
                        <input type="hidden" name="add_category" value="1">
                        <div class="mb-3">
                            <label for="category_name" class="form-label">اسم الفئة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="category_name" name="category_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="category_description" class="form-label">وصف الفئة</label>
                            <textarea class="form-control" id="category_description" name="category_description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="addCategoryForm" class="btn btn-primary">إضافة الفئة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        // تهيئة جدول المنتجات
        $(document).ready(function() {
            $('#productsTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
                },
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "الكل"]]
            });
        });
        
        // دالة لتعديل المنتج
        function editProduct(productId) {
            // هنا يمكن إضافة كود AJAX لجلب بيانات المنتج من الخادم
            // لأغراض العرض، سنستخدم بيانات وهمية
            
            // في التطبيق الحقيقي، يجب استبدال هذا بطلب AJAX
            fetch('get_product.php?id=' + productId)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('edit_product_id').value = data.id;
                    document.getElementById('edit_name').value = data.name;
                    document.getElementById('edit_barcode').value = data.barcode;
                    document.getElementById('edit_category_id').value = data.category_id;
                    document.getElementById('edit_purchase_price').value = data.purchase_price;
                    document.getElementById('edit_price').value = data.price;
                    document.getElementById('edit_stock').value = data.stock;
                    document.getElementById('edit_min_stock').value = data.min_stock;
                    document.getElementById('edit_description').value = data.description;
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء جلب بيانات المنتج');
                });
        }
        
        // دالة لتحديث المخزون
        function updateStock(productId) {
            // هنا يمكن إضافة كود AJAX لجلب بيانات المنتج من الخادم
            // لأغراض العرض، سنستخدم بيانات وهمية
            
            // في التطبيق الحقيقي، يجب استبدال هذا بطلب AJAX
            fetch('get_product.php?id=' + productId)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('stock_product_id').value = data.id;
                    document.getElementById('product_name').value = data.name;
                    document.getElementById('current_stock').value = data.stock;
                    document.getElementById('new_stock').value = data.stock;
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء جلب بيانات المنتج');
                });
        }
        
        // دالة لتأكيد حذف المنتج
        function confirmDelete(productId) {
            if (confirm('هل أنت متأكد من رغبتك في حذف هذا المنتج؟')) {
                window.location.href = 'products.php?delete=' + productId;
            }
        }
        
        // حساب الربح ونسبة الربح عند تغيير سعر الشراء أو البيع
        document.getElementById('purchase_price').addEventListener('input', calculateProfit);
        document.getElementById('price').addEventListener('input', calculateProfit);
        document.getElementById('edit_purchase_price').addEventListener('input', calculateEditProfit);
        document.getElementById('edit_price').addEventListener('input', calculateEditProfit);
        
        function calculateProfit() {
            const purchasePrice = parseFloat(document.getElementById('purchase_price').value) || 0;
            const sellingPrice = parseFloat(document.getElementById('price').value) || 0;
            
            if (purchasePrice > 0 && sellingPrice > 0) {
                const profit = sellingPrice - purchasePrice;
                const profitPercentage = (profit / purchasePrice) * 100;
                
                // يمكن عرض الربح ونسبة الربح في عنصر HTML
                console.log(`الربح: ${profit.toFixed(2)} ر.س (${profitPercentage.toFixed(2)}%)`);
            }
        }
        
        function calculateEditProfit() {
            const purchasePrice = parseFloat(document.getElementById('edit_purchase_price').value) || 0;
            const sellingPrice = parseFloat(document.getElementById('edit_price').value) || 0;
            
            if (purchasePrice > 0 && sellingPrice > 0) {
                const profit = sellingPrice - purchasePrice;
                const profitPercentage = (profit / purchasePrice) * 100;
                
                // يمكن عرض الربح ونسبة الربح في عنصر HTML
                console.log(`الربح: ${profit.toFixed(2)} ر.س (${profitPercentage.toFixed(2)}%)`);
            }
        }
    </script>
</body>
</html>