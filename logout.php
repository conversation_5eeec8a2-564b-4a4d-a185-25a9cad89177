<?php
// Include database connection
require_once 'db_connect.php';

// Start session
session_start();

// Check if user is logged in
if (isset($_SESSION['user_id'])) {
    $userId = $_SESSION['user_id'];
    $sessionId = session_id();
    
    // Update session record
    $stmt = $conn->prepare("UPDATE sessions SET last_activity = NOW() WHERE session_id = ?");
    $stmt->bind_param("s", $sessionId);
    $stmt->execute();
    $stmt->close();
    
    // Log user logout
    $action = "تسجيل الخروج";
    $details = "تم تسجيل الخروج بنجاح";
    
    $logStmt = $conn->prepare("INSERT INTO user_logs (user_id, action, details) VALUES (?, ?, ?)");
    $logStmt->bind_param("iss", $userId, $action, $details);
    $logStmt->execute();
    $logStmt->close();
    
    // Close connection
    closeConnection($conn);
}

// Destroy session
session_unset();
session_destroy();

// Redirect to login page
header('Location: index.html');
exit;
?>