-- تحديث جدول المشتريات لإضافة الأعمدة المفقودة
-- يمكن تشغيل هذا الملف يدوياً إذا لم تتم إضافة الأعمدة تلقائياً

USE tec_store;

-- إضا<PERSON>ة عمود invoice_number إذا لم يكن موجوداً
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = 'tec_store' 
                     AND TABLE_NAME = 'purchases' 
                     AND COLUMN_NAME = 'invoice_number');

SET @sql = IF(@column_exists = 0, 
              'ALTER TABLE purchases ADD COLUMN invoice_number VARCHAR(50) NOT NULL DEFAULT \'\'', 
              'SELECT "Column invoice_number already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة عمود purchase_date إذا لم يكن موجوداً
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = 'tec_store' 
                     AND TABLE_NAME = 'purchases' 
                     AND COLUMN_NAME = 'purchase_date');

SET @sql = IF(@column_exists = 0, 
              'ALTER TABLE purchases ADD COLUMN purchase_date DATE NOT NULL DEFAULT (CURDATE())', 
              'SELECT "Column purchase_date already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة عمود payment_method إذا لم يكن موجوداً
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = 'tec_store' 
                     AND TABLE_NAME = 'purchases' 
                     AND COLUMN_NAME = 'payment_method');

SET @sql = IF(@column_exists = 0, 
              'ALTER TABLE purchases ADD COLUMN payment_method VARCHAR(50) NULL', 
              'SELECT "Column payment_method already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة عمود payment_status إذا لم يكن موجوداً
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = 'tec_store' 
                     AND TABLE_NAME = 'purchases' 
                     AND COLUMN_NAME = 'payment_status');

SET @sql = IF(@column_exists = 0, 
              'ALTER TABLE purchases ADD COLUMN payment_status ENUM(\'paid\', \'partial\', \'unpaid\') NOT NULL DEFAULT \'unpaid\'', 
              'SELECT "Column payment_status already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة عمود id إذا لم يكن موجوداً (للتوافق مع الكود)
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = 'tec_store' 
                     AND TABLE_NAME = 'purchases' 
                     AND COLUMN_NAME = 'id');

SET @purchase_id_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                          WHERE TABLE_SCHEMA = 'tec_store' 
                          AND TABLE_NAME = 'purchases' 
                          AND COLUMN_NAME = 'purchase_id');

-- إذا كان purchase_id موجود ولكن id غير موجود، نضيف id
SET @sql = IF(@column_exists = 0 AND @purchase_id_exists > 0, 
              'ALTER TABLE purchases ADD COLUMN id INT(11) NOT NULL AUTO_INCREMENT UNIQUE KEY AFTER purchase_id', 
              'SELECT "Column id already exists or purchase_id not found" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- نسخ البيانات من purchase_id إلى id إذا كان ذلك ضرورياً
SET @sql = IF(@column_exists = 0 AND @purchase_id_exists > 0,
              'UPDATE purchases SET id = purchase_id WHERE id IS NULL OR id = 0',
              'SELECT "No data copy needed" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة عمود user_id إذا لم يكن موجوداً
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                     WHERE TABLE_SCHEMA = 'tec_store'
                     AND TABLE_NAME = 'purchases'
                     AND COLUMN_NAME = 'user_id');

SET @sql = IF(@column_exists = 0,
              'ALTER TABLE purchases ADD COLUMN user_id INT(11) NOT NULL DEFAULT 1',
              'SELECT "Column user_id already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة المفتاح الخارجي لـ user_id إذا لم يكن موجوداً
SET @constraint_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                         WHERE TABLE_SCHEMA = 'tec_store'
                         AND TABLE_NAME = 'purchases'
                         AND COLUMN_NAME = 'user_id'
                         AND REFERENCED_TABLE_NAME = 'users');

SET @users_table_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
                          WHERE TABLE_SCHEMA = 'tec_store'
                          AND TABLE_NAME = 'users');

SET @sql = IF(@constraint_exists = 0 AND @users_table_exists > 0 AND @column_exists > 0,
              'ALTER TABLE purchases ADD CONSTRAINT fk_purchases_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT',
              'SELECT "Foreign key constraint already exists or not needed" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT 'Database update completed successfully!' as result;
