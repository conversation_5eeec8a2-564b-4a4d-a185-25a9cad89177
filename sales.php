<?php
// بدء جلسة
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// استيراد ملف الاتصال بقاعدة البيانات
require_once 'config/db_connection.php';

// استعلام لجلب المنتجات
$products_query = "SELECT * FROM products WHERE stock > 0 ORDER BY name ASC";
$products_result = mysqli_query($conn, $products_query);

// استعلام لجلب العملاء
$customers_query = "SELECT * FROM customers ORDER BY name ASC";
$customers_result = mysqli_query($conn, $customers_query);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام نقطة البيع - إدارة المتجر</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Barcode Generator -->
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --whatsapp-color: #25D366;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --sidebar-width: 250px;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fb;
            color: #333;
            overflow-x: hidden;
        }
        
        /* تصميم الشريط الجانبي */
        .sidebar {
            height: 100vh;
            width: var(--sidebar-width);
            position: fixed;
            top: 0;
            right: 0;
            background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding-top: 20px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            z-index: 1000;
        }
        
        .sidebar .logo {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .logo h3 {
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 15px;
            border-radius: 8px;
            transition: all 0.3s;
            font-size: 16px;
        }
        
        .sidebar .nav-link:hover, 
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar .nav-link i {
            margin-left: 10px;
            width: 25px;
            text-align: center;
        }
        
        /* تصميم المحتوى الرئيسي */
        .main-content {
            margin-right: var(--sidebar-width);
            padding: 20px;
            transition: all 0.3s;
        }
        
        /* شريط التنقل العلوي */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        
        .search-bar {
            background-color: #f8f9fa;
            border-radius: 30px;
            padding: 8px 20px;
            border: 1px solid #eaeaea;
        }
        
        .search-bar input {
            background: transparent;
            border: none;
            width: 100%;
            padding: 5px 10px;
        }
        
        .search-bar input:focus {
            outline: none;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #f94144;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* بطاقات الإحصائيات */
        .stat-card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .stat-card .card-title {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-card .card-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .stat-card .card-footer {
            background: transparent;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 13px;
            padding: 8px 15px;
        }
        
        /* تصميم صفحة المبيعات */
        .pos-container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .products-section {
            flex: 1;
            min-width: 300px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
        }
        
        .cart-section {
            flex: 1;
            min-width: 300px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
        }
        
        .customer-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-top: 20px;
        }
        
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .product-card {
            border: 1px solid #eaeaea;
            border-radius: 10px;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .product-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-color: var(--primary-color);
        }
        
        .product-card img {
            width: 60px;
            height: 60px;
            object-fit: contain;
            margin-bottom: 10px;
        }
        
        .product-card .product-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .product-card .product-price {
            font-size: 16px;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .cart-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .cart-item .item-info {
            flex-grow: 1;
        }
        
        .cart-item .item-actions {
            display: flex;
            align-items: center;
        }
        
        .cart-item .item-actions .quantity {
            margin: 0 10px;
            font-weight: bold;
        }
        
        .cart-item .item-actions button {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .cart-summary {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 2px solid #eee;
        }
        
        .cart-summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .payment-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .payment-btn {
            margin-top: 15px;
            width: 100%;
            padding: 12px;
            font-size: 16px;
            font-weight: 600;
        }
        
        .customer-card {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #eaeaea;
            border-radius: 10px;
            margin-bottom: 10px;
            cursor: pointer;
        }
        
        .customer-card.active {
            border-color: var(--primary-color);
            background-color: rgba(67, 97, 238, 0.05);
        }
        
        .customer-card img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            margin-left: 15px;
        }
        
        .balance-info {
            display: flex;
            gap: 20px;
            margin-top: 15px;
        }
        
        .balance-card {
            flex: 1;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        
        .balance-card .title {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .balance-card .amount {
            font-size: 18px;
            font-weight: 700;
        }
        
        .balance-card .positive {
            color: var(--success-color);
        }
        
        .balance-card .negative {
            color: var(--danger-color);
        }
        
        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
                overflow: hidden;
            }
            
            .sidebar .logo h3, .sidebar .nav-link span {
                display: none;
            }
            
            .sidebar .nav-link {
                text-align: center;
                margin: 5px 10px;
            }
            
            .sidebar .nav-link i {
                margin-left: 0;
            }
            
            .main-content {
                margin-right: 70px;
            }
            
            .pos-container {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-store"></i> متجرنا</h3>
        </div>
        <ul class="nav flex-column mt-4">
            <li class="nav-item">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="sales.php">
                    <i class="fas fa-shopping-cart"></i>
                    <span>المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales_management.php">
                    <i class="fas fa-chart-line"></i>
                    <span>إدارة المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="products.php">
                    <i class="fas fa-box"></i>
                    <span>المنتجات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customers.php">
                    <i class="fas fa-users"></i>
                    <span>العملاء</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="suppliers.php">
                    <i class="fas fa-truck"></i>
                    <span>الموردين</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="purchases.php">
                    <i class="fas fa-shopping-basket"></i>
                    <span>المشتريات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="expenses.php">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>المصروفات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="financial.php">
                    <i class="fas fa-chart-pie"></i>
                    <span>الإدارة المالية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="barcode.php">
                    <i class="fas fa-barcode"></i>
                    <span>مولد الباركود</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="settings.php">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط التنقل العلوي -->
        <div class="top-navbar">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4 class="mb-0">نقطة البيع</h4>
                    <p class="mb-0 text-muted">مرحباً بعودتك، <?php echo $_SESSION['username']; ?>!</p>
                </div>
                <div class="col-md-6 d-flex justify-content-end">
                    <div class="d-flex align-items-center">
                        <div class="search-bar me-3">
                            <i class="fas fa-search"></i>
                            <input type="text" id="productSearch" placeholder="بحث عن منتج...">
                        </div>
                        <div class="position-relative me-3">
                            <button class="btn btn-light rounded-circle">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-light rounded-circle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.php">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نظام نقطة البيع -->
        <div class="pos-container">
            <!-- قسم المنتجات -->
            <div class="products-section">
                <h5>المنتجات</h5>
                <div class="input-group mb-3">
                    <input type="text" class="form-control" placeholder="بحث عن منتج..." id="productSearchInput">
                    <button class="btn btn-primary" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                
                <div class="product-categories mb-3">
                    <div class="btn-group w-100">
                        <button class="btn btn-outline-primary active">الكل</button>
                        <button class="btn btn-outline-primary">إلكترونيات</button>
                        <button class="btn btn-outline-primary">ملابس</button>
                        <button class="btn btn-outline-primary">أدوات منزلية</button>
                    </div>
                </div>
                
                <div class="product-grid">
                    <?php
                    // عرض المنتجات
                    if (mysqli_num_rows($products_result) > 0) {
                        while ($product = mysqli_fetch_assoc($products_result)) {
                            echo '<div class="product-card" data-id="' . $product['id'] . '" data-name="' . $product['name'] . '" data-price="' . $product['price'] . '">';
                            echo '<img src="' . (!empty($product['image']) ? $product['image'] : 'assets/img/product-placeholder.png') . '" alt="' . $product['name'] . '">';
                            echo '<div class="product-name">' . $product['name'] . '</div>';
                            echo '<div class="product-price">' . $product['price'] . ' ر.س</div>';
                            echo '</div>';
                        }
                    } else {
                        echo '<div class="alert alert-info">لا توجد منتجات متاحة حالياً</div>';
                    }
                    ?>
                </div>
            </div>
            
            <!-- قسم سلة التسوق -->
            <div class="cart-section">
                <h5>سلة المشتريات</h5>
                
                <div class="cart-items" id="cartItems">
                    <!-- ستتم إضافة عناصر السلة هنا بواسطة JavaScript -->
                    <div class="text-center py-4 text-muted">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>السلة فارغة</p>
                    </div>
                </div>
                
                <div class="cart-summary">
                    <div class="cart-summary-row">
                        <span>المجموع:</span>
                        <span id="subtotal">0.00 ر.س</span>
                    </div>
                    <div class="cart-summary-row">
                        <span>الضريبة (15%):</span>
                        <span id="tax">0.00 ر.س</span>
                    </div>
                    <div class="cart-summary-row">
                        <span>الخصم:</span>
                        <div>
                            <input type="number" class="form-control form-control-sm" id="discountInput" min="0" value="0">
                        </div>
                    </div>
                    <div class="cart-summary-row">
                        <span>الإجمالي:</span>
                        <span id="total" class="fw-bold">0.00 ر.س</span>
                    </div>
                </div>
                
                <div class="payment-section">
                    <h6>طريقة الدفع</h6>
                    <div class="btn-group w-100 mb-3">
                        <button class="btn btn-outline-primary active" id="cashPayment">نقدي</button>
                        <button class="btn btn-outline-primary" id="cardPayment">بطاقة</button>
                        <button class="btn btn-outline-primary" id="creditPayment">آجل</button>
                    </div>
                    
                    <div id="cashPaymentForm">
                        <div class="mb-3">
                            <label for="amountPaid" class="form-label">المبلغ المدفوع</label>
                            <input type="number" class="form-control" id="amountPaid" min="0" step="0.01">
                        </div>
                        <div class="mb-3">
                            <label for="change" class="form-label">المتبقي</label>
                            <input type="text" class="form-control" id="change" readonly>
                        </div>
                    </div>
                    
                    <div id="creditPaymentForm" style="display: none;">
                        <div class="mb-3">
                            <label for="amountPaidCredit" class="form-label">المبلغ المدفوع (اختياري)</label>
                            <input type="number" class="form-control" id="amountPaidCredit" min="0" step="0.01" value="0">
                        </div>
                        <div class="mb-3">
                            <label for="remainingCredit" class="form-label">المبلغ المتبقي (دين)</label>
                            <input type="text" class="form-control" id="remainingCredit" readonly>
                        </div>
                    </div>
                    
                    <button class="btn btn-primary payment-btn" id="completePayment">
                        <i class="fas fa-check-circle me-2"></i> إتمام عملية الدفع
                    </button>
                </div>
            </div>
        </div>
        
        <!-- قسم العميل -->
        <div class="customer-section mt-4">
            <h5>اختيار العميل</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" placeholder="بحث عن عميل..." id="customerSearchInput">
                        <button class="btn btn-primary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    
                    <div class="customer-list">
                        <?php
                        // عرض العملاء
                        if (mysqli_num_rows($customers_result) > 0) {
                            while ($customer = mysqli_fetch_assoc($customers_result)) {
                                echo '<div class="customer-card" data-id="' . $customer['id'] . '" data-name="' . $customer['name'] . '" data-wallet="' . ($customer['wallet_balance'] ?? 0) . '" data-debt="' . ($customer['debt_balance'] ?? 0) . '">';
                                echo '<img src="assets/img/user-placeholder.png" alt="' . $customer['name'] . '">';
                                echo '<div>';
                                echo '<h6>' . $customer['name'] . '</h6>';
                                echo '<small class="text-muted">' . $customer['phone'] . '</small>';
                                echo '</div>';
                                echo '</div>';
                            }
                        } else {
                            echo '<div class="alert alert-info">لا يوجد عملاء مسجلين</div>';
                        }
                        ?>
                        <button class="btn btn-outline-primary w-100 mt-2" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                            <i class="fas fa-plus"></i> إضافة عميل جديد
                        </button>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="selected-customer-info" id="selectedCustomerInfo">
                        <div class="text-center py-4 text-muted">
                            <i class="fas fa-user-circle fa-3x mb-3"></i>
                            <p>لم يتم اختيار عميل</p>
                        </div>
                    </div>
                    
                    <div class="balance-info" id="balanceInfo" style="display: none;">
                        <div class="balance-card">
                            <div class="title">الرصيد المحفوظ</div>
                            <div class="amount positive" id="walletBalance">0.00 ر.س</div>
                        </div>
                        <div class="balance-card">
                            <div class="title">الرصيد المدين</div>
                            <div class="amount negative" id="debtBalance">0.00 ر.س</div>
                        </div>
                    </div>
                    
                    <div class="wallet-usage mt-3" id="walletUsage" style="display: none;">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="useWalletBalance">
                            <label class="form-check-label" for="useWalletBalance">
                                استخدام الرصيد المحفوظ في الدفع
                            </label>
                        </div>
                        <div id="walletAmountContainer" style="display: none;">
                            <div class="input-group mt-2">
                                <span class="input-group-text">المبلغ</span>
                                <input type="number" class="form-control" id="walletAmount" min="0" step="0.01">
                                <button class="btn btn-outline-primary" type="button" id="useMaxWallet">استخدام الكل</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال إضافة عميل جديد -->
    <div class="modal fade" id="addCustomerModal" tabindex="-1" aria-labelledby="addCustomerModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCustomerModalLabel">إضافة عميل جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addCustomerForm">
                        <div class="mb-3">
                            <label for="customerName" class="form-label">اسم العميل</label>
                            <input type="text" class="form-control" id="customerName" required>
                        </div>
                        <div class="mb-3">
                            <label for="customerPhone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="customerPhone" required>
                        </div>
                        <div class="mb-3">
                            <label for="customerEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="customerEmail">
                        </div>
                        <div class="mb-3">
                            <label for="customerAddress" class="form-label">العنوان</label>
                            <textarea class="form-control" id="customerAddress" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveCustomer">حفظ</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال تأكيد الدفع -->
    <div class="modal fade" id="paymentConfirmModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد عملية الدفع</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="payment-details">
                        <div class="row mb-2">
                            <div class="col-6">إجمالي الفاتورة:</div>
                            <div class="col-6 text-end fw-bold" id="modalTotal">0.00 ر.س</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-6">المبلغ المدفوع:</div>
                            <div class="col-6 text-end" id="modalPaid">0.00 ر.س</div>
                        </div>
                        <div class="row mb-2" id="modalWalletRow" style="display: none;">
                            <div class="col-6">المدفوع من الرصيد:</div>
                            <div class="col-6 text-end" id="modalWallet">0.00 ر.س</div>
                        </div>
                        <div class="row mb-2" id="modalChangeRow">
                            <div class="col-6">المتبقي للعميل:</div>
                            <div class="col-6 text-end text-success" id="modalChange">0.00 ر.س</div>
                        </div>
                        <div class="row mb-2" id="modalDebtRow" style="display: none;">
                            <div class="col-6">المبلغ المتبقي (دين):</div>
                            <div class="col-6 text-end text-danger" id="modalDebt">0.00 ر.س</div>
                        </div>
                        <div class="row mb-2" id="modalSaveBalanceRow" style="display: none;">
                            <div class="col-6">حفظ المتبقي في رصيد العميل:</div>
                            <div class="col-6 text-end">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="saveBalance" checked>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="confirmPayment">تأكيد</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // متغيرات عامة
            let cart = [];
            let selectedCustomer = null;
            let paymentMethod = 'cash';
            
            // إضافة منتج إلى السلة
            const productCards = document.querySelectorAll('.product-card');
            productCards.forEach(card => {
                card.addEventListener('click', function() {
                    const productId = this.dataset.id;
                    const productName = this.dataset.name;
                    const productPrice = parseFloat(this.dataset.price);
                    
                    // التحقق مما إذا كان المنتج موجودًا بالفعل في السلة
                    const existingItem = cart.find(item => item.id === productId);
                    
                    if (existingItem) {
                        existingItem.quantity += 1;
                    } else {
                        cart.push({
                            id: productId,
                            name: productName,
                            price: productPrice,
                            quantity: 1
                        });
                    }
                    
                    updateCart();
                });
            });
            
            // تحديث عرض السلة
            function updateCart() {
                const cartItemsContainer = document.getElementById('cartItems');
                
                if (cart.length === 0) {
                    cartItemsContainer.innerHTML = `
                        <div class="text-center py-4 text-muted">
                            <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                            <p>السلة فارغة</p>
                        </div>
                    `;
                    document.getElementById('subtotal').textContent = '0.00 ر.س';
                    document.getElementById('tax').textContent = '0.00 ر.س';
                    document.getElementById('total').textContent = '0.00 ر.س';
                    return;
                }
                
                let cartHTML = '';
                let subtotal = 0;
                
                cart.forEach(item => {
                    const itemTotal = item.price * item.quantity;
                    subtotal += itemTotal;
                    
                    cartHTML += `
                        <div class="cart-item" data-id="${item.id}">
                            <div class="item-info">
                                <div class="fw-bold">${item.name}</div>
                                <div class="text-muted">${item.price.toFixed(2)} ر.س</div>
                            </div>
                            <div class="item-actions">
                                <button class="btn btn-sm btn-outline-danger decrease-quantity">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <span class="quantity mx-2">${item.quantity}</span>
                                <button class="btn btn-sm btn-outline-primary increase-quantity">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger ms-2 remove-item">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                });
                
                cartItemsContainer.innerHTML = cartHTML;
                
                // حساب الضريبة والإجمالي
                const discount = parseFloat(document.getElementById('discountInput').value) || 0;
                const tax = subtotal * 0.15; // ضريبة القيمة المضافة 15%
                const total = subtotal + tax - discount;
                
                document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ر.س';
                document.getElementById('tax').textContent = tax.toFixed(2) + ' ر.س';
                document.getElementById('total').textContent = total.toFixed(2) + ' ر.س';
                
                // تحديث حقل المتبقي في نموذج الدفع
                updateChangeAmount();
                
                // إضافة مستمعي الأحداث لأزرار السلة
                addCartButtonListeners();
            }
            
            // إضافة مستمعي الأحداث لأزرار السلة
            function addCartButtonListeners() {
                // زيادة الكمية
                document.querySelectorAll('.increase-quantity').forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.stopPropagation();
                        const cartItem = this.closest('.cart-item');
                        const productId = cartItem.dataset.id;
                        const item = cart.find(item => item.id === productId);
                        
                        if (item) {
                            item.quantity += 1;
                            updateCart();
                        }
                    });
                });
                
                // تقليل الكمية
                document.querySelectorAll('.decrease-quantity').forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.stopPropagation();
                        const cartItem = this.closest('.cart-item');
                        const productId = cartItem.dataset.id;
                        const item = cart.find(item => item.id === productId);
                        
                        if (item) {
                            item.quantity -= 1;
                            
                            if (item.quantity <= 0) {
                                cart = cart.filter(i => i.id !== productId);
                            }
                            
                            updateCart();
                        }
                    });
                });
                
                // إزالة العنصر
                document.querySelectorAll('.remove-item').forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.stopPropagation();
                        const cartItem = this.closest('.cart-item');
                        const productId = cartItem.dataset.id;
                        
                        cart = cart.filter(item => item.id !== productId);
                        updateCart();
                    });
                });
            }
            
            // تحديث المبلغ المتبقي
            function updateChangeAmount() {
                const totalAmount = parseFloat(document.getElementById('total').textContent);
                let amountPaid = 0;
                
                if (paymentMethod === 'cash') {
                    amountPaid = parseFloat(document.getElementById('amountPaid').value) || 0;
                    const change = amountPaid - totalAmount;
                    document.getElementById('change').value = change >= 0 ? change.toFixed(2) + ' ر.س' : '0.00 ر.س';
                } else if (paymentMethod === 'credit') {
                    amountPaid = parseFloat(document.getElementById('amountPaidCredit').value) || 0;
                    const remaining = totalAmount - amountPaid;
                    document.getElementById('remainingCredit').value = remaining.toFixed(2) + ' ر.س';
                }
            }
            
            // مستمع حدث لتغيير المبلغ المدفوع
            document.getElementById('amountPaid').addEventListener('input', updateChangeAmount);
            document.getElementById('amountPaidCredit').addEventListener('input', updateChangeAmount);
            document.getElementById('discountInput').addEventListener('input', updateCart);
            
            // مستمعي أحداث لأزرار طرق الدفع
            document.getElementById('cashPayment').addEventListener('click', function() {
                paymentMethod = 'cash';
                this.classList.add('active');
                document.getElementById('cardPayment').classList.remove('active');
                document.getElementById('creditPayment').classList.remove('active');
                document.getElementById('cashPaymentForm').style.display = 'block';
                document.getElementById('creditPaymentForm').style.display = 'none';
            });
            
            document.getElementById('cardPayment').addEventListener('click', function() {
                paymentMethod = 'card';
                this.classList.add('active');
                document.getElementById('cashPayment').classList.remove('active');
                document.getElementById('creditPayment').classList.remove('active');
                document.getElementById('cashPaymentForm').style.display = 'none';
                document.getElementById('creditPaymentForm').style.display = 'none';
            });
            
            document.getElementById('creditPayment').addEventListener('click', function() {
                paymentMethod = 'credit';
                this.classList.add('active');
                document.getElementById('cashPayment').classList.remove('active');
                document.getElementById('cardPayment').classList.remove('active');
                document.getElementById('cashPaymentForm').style.display = 'none';
                document.getElementById('creditPaymentForm').style.display = 'block';
                updateChangeAmount();
            });
            
            // اختيار العميل
            const customerCards = document.querySelectorAll('.customer-card');
            customerCards.forEach(card => {
                card.addEventListener('click', function() {
                    // إزالة الفئة النشطة من جميع البطاقات
                    customerCards.forEach(c => c.classList.remove('active'));
                    
                    // إضافة الفئة النشطة إلى البطاقة المحددة
                    this.classList.add('active');
                    
                    // تخزين بيانات العميل المحدد
                    selectedCustomer = {
                        id: this.dataset.id,
                        name: this.dataset.name,
                        walletBalance: parseFloat(this.dataset.wallet) || 0,
                        debtBalance: parseFloat(this.dataset.debt) || 0
                    };
                    
                    // تحديث معلومات العميل المعروضة
                    updateSelectedCustomerInfo();
                });
            });
            
            // تحديث معلومات العميل المحدد
            function updateSelectedCustomerInfo() {
                const customerInfoContainer = document.getElementById('selectedCustomerInfo');
                const balanceInfo = document.getElementById('balanceInfo');
                const walletUsage = document.getElementById('walletUsage');
                
                if (!selectedCustomer) {
                    customerInfoContainer.innerHTML = `
                        <div class="text-center py-4 text-muted">
                            <i class="fas fa-user-circle fa-3x mb-3"></i>
                            <p>لم يتم اختيار عميل</p>
                        </div>
                    `;
                    balanceInfo.style.display = 'none';
                    walletUsage.style.display = 'none';
                    return;
                }
                
                customerInfoContainer.innerHTML = `
                    <div class="d-flex align-items-center mb-3">
                        <img src="assets/img/user-placeholder.png" alt="${selectedCustomer.name}" class="rounded-circle me-3" width="60" height="60">
                        <div>
                            <h5 class="mb-1">${selectedCustomer.name}</h5>
                            <button class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-history me-1"></i> سجل المعاملات
                            </button>
                        </div>
                    </div>
                `;
                
                // عرض معلومات الرصيد
                document.getElementById('walletBalance').textContent = selectedCustomer.walletBalance.toFixed(2) + ' ر.س';
                document.getElementById('debtBalance').textContent = selectedCustomer.debtBalance.toFixed(2) + ' ر.س';
                balanceInfo.style.display = 'flex';
                
                // عرض خيار استخدام الرصيد إذا كان الرصيد المحفوظ أكبر من 0
                if (selectedCustomer.walletBalance > 0) {
                    walletUsage.style.display = 'block';
                } else {
                    walletUsage.style.display = 'none';
                }
            }
            
            // مستمع حدث لاستخدام الرصيد المحفوظ
            document.getElementById('useWalletBalance').addEventListener('change', function() {
                const walletAmountContainer = document.getElementById('walletAmountContainer');
                
                if (this.checked) {
                    walletAmountContainer.style.display = 'block';
                } else {
                    walletAmountContainer.style.display = 'none';
                }
            });
            
            // مستمع حدث لاستخدام الحد الأقصى للرصيد
            document.getElementById('useMaxWallet').addEventListener('click', function() {
                if (selectedCustomer) {
                    const totalAmount = parseFloat(document.getElementById('total').textContent);
                    const maxAmount = Math.min(selectedCustomer.walletBalance, totalAmount);
                    document.getElementById('walletAmount').value = maxAmount.toFixed(2);
                }
            });
            
            // مستمع حدث لإتمام عملية الدفع
            document.getElementById('completePayment').addEventListener('click', function() {
                // التحقق من وجود منتجات في السلة
                if (cart.length === 0) {
                    alert('الرجاء إضافة منتجات إلى السلة أولاً');
                    return;
                }
                
                // التحقق من اختيار عميل
                if (!selectedCustomer) {
                    alert('الرجاء اختيار عميل أولاً');
                    return;
                }
                
                // الحصول على بيانات الدفع
                const totalAmount = parseFloat(document.getElementById('total').textContent);
                let amountPaid = 0;
                let walletAmount = 0;
                let change = 0;
                let debt = 0;
                
                // حساب المبالغ حسب طريقة الدفع
                if (paymentMethod === 'cash') {
                    amountPaid = parseFloat(document.getElementById('amountPaid').value) || 0;
                    
                    if (amountPaid < totalAmount) {
                        alert('المبلغ المدفوع أقل من إجمالي الفاتورة. الرجاء استخدام الدفع الآجل.');
                        return;
                    }
                    
                    change = amountPaid - totalAmount;
                } else if (paymentMethod === 'card') {
                    amountPaid = totalAmount;
                } else if (paymentMethod === 'credit') {
                    amountPaid = parseFloat(document.getElementById('amountPaidCredit').value) || 0;
                    debt = totalAmount - amountPaid;
                }
                
                // التحقق من استخدام الرصيد المحفوظ
                if (document.getElementById('useWalletBalance').checked) {
                    walletAmount = parseFloat(document.getElementById('walletAmount').value) || 0;
                    
                    if (walletAmount > selectedCustomer.walletBalance) {
                        alert('المبلغ المستخدم من الرصيد أكبر من الرصيد المتاح');
                        return;
                    }
                    
                    // تعديل المبالغ بناءً على استخدام الرصيد
                    if (paymentMethod === 'cash') {
                        change = amountPaid - (totalAmount - walletAmount);
                    } else if (paymentMethod === 'credit') {
                        debt = totalAmount - amountPaid - walletAmount;
                    }
                }
                
                // عرض مودال تأكيد الدفع
                document.getElementById('modalTotal').textContent = totalAmount.toFixed(2) + ' ر.س';
                document.getElementById('modalPaid').textContent = amountPaid.toFixed(2) + ' ر.س';
                
                // عرض/إخفاء صفوف المودال حسب طريقة الدفع
                if (walletAmount > 0) {
                    document.getElementById('modalWalletRow').style.display = 'flex';
                    document.getElementById('modalWallet').textContent = walletAmount.toFixed(2) + ' ر.س';
                } else {
                    document.getElementById('modalWalletRow').style.display = 'none';
                }
                
                if (paymentMethod === 'cash' || paymentMethod === 'card') {
                    document.getElementById('modalChangeRow').style.display = 'flex';
                    document.getElementById('modalChange').textContent = change.toFixed(2) + ' ر.س';
                    document.getElementById('modalDebtRow').style.display = 'none';
                    
                    // عرض خيار حفظ المتبقي في رصيد العميل إذا كان هناك متبقي
                    if (change > 0) {
                        document.getElementById('modalSaveBalanceRow').style.display = 'flex';
                    } else {
                        document.getElementById('modalSaveBalanceRow').style.display = 'none';
                    }
                } else if (paymentMethod === 'credit') {
                    document.getElementById('modalChangeRow').style.display = 'none';
                    document.getElementById('modalDebtRow').style.display = 'flex';
                    document.getElementById('modalDebt').textContent = debt.toFixed(2) + ' ر.س';
                    document.getElementById('modalSaveBalanceRow').style.display = 'none';
                }
                
                // عرض المودال
                const paymentConfirmModal = new bootstrap.Modal(document.getElementById('paymentConfirmModal'));
                paymentConfirmModal.show();
            });
            
            // مستمع حدث لتأكيد الدفع
            document.getElementById('confirmPayment').addEventListener('click', function() {
                // هنا سيتم إرسال بيانات الفاتورة إلى الخادم
                // يمكن استخدام AJAX لإرسال البيانات
                
                // بعد نجاح العملية، إعادة تعيين السلة والنموذج
                cart = [];
                selectedCustomer = null;
                updateCart();
                updateSelectedCustomerInfo();
                
                // إغلاق المودال
                const paymentConfirmModal = bootstrap.Modal.getInstance(document.getElementById('paymentConfirmModal'));
                paymentConfirmModal.hide();
                
                // عرض رسالة نجاح
                alert('تم إتمام عملية الدفع بنجاح');
            });
            
            // مستمع حدث لإضافة عميل جديد
            document.getElementById('saveCustomer').addEventListener('click', function() {
                // التحقق من صحة النموذج
                const customerName = document.getElementById('customerName').value;
                const customerPhone = document.getElementById('customerPhone').value;
                
                if (!customerName || !customerPhone) {
                    alert('الرجاء إدخال اسم العميل ورقم الهاتف');
                    return;
                }
                
                // هنا سيتم إرسال بيانات العميل الجديد إلى الخادم
                // يمكن استخدام AJAX لإرسال البيانات
                
                // إغلاق المودال
                const addCustomerModal = bootstrap.Modal.getInstance(document.getElementById('addCustomerModal'));
                addCustomerModal.hide();
                
                // عرض رسالة نجاح
                alert('تم إضافة العميل بنجاح');
                
                // إعادة تعيين النموذج
                document.getElementById('addCustomerForm').reset();
            });
            
            // البحث عن المنتجات
            document.getElementById('productSearchInput').addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                
                productCards.forEach(card => {
                    const productName = card.dataset.name.toLowerCase();
                    
                    if (productName.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
            
            // البحث عن العملاء
            document.getElementById('customerSearchInput').addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                
                customerCards.forEach(card => {
                    const customerName = card.dataset.name.toLowerCase();
                    
                    if (customerName.includes(searchTerm)) {
                        card.style.display = 'flex';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html>