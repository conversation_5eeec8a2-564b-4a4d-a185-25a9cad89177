// ملف رسم بياني بسيط وثابت
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من وجود عنصر الرسم البياني
    var chartElement = document.getElementById('salesChart');
    if (!chartElement) return;
    
    // بيانات ثابتة
    var weekData = {
        labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
        datasets: [{
            label: 'المبيعات',
            data: [3200, 2800, 4500, 3800, 5200, 4900, 6000],
            backgroundColor: 'rgba(67, 97, 238, 0.1)',
            borderColor: '#4361ee',
            borderWidth: 2,
            tension: 0.3,
            pointBackgroundColor: '#4361ee',
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 5,
            fill: true
        }]
    };
    
    var dayData = {
        labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
        datasets: [{
            label: 'المبيعات',
            data: [800, 1200, 950, 1400, 1100, 1300, 1500],
            backgroundColor: 'rgba(67, 97, 238, 0.1)',
            borderColor: '#4361ee',
            borderWidth: 2,
            tension: 0.3,
            pointBackgroundColor: '#4361ee',
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 5,
            fill: true
        }]
    };
    
    var monthData = {
        labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
        datasets: [{
            label: 'المبيعات',
            data: [12000, 15000, 13500, 16000, 14500, 17000, 18500],
            backgroundColor: 'rgba(67, 97, 238, 0.1)',
            borderColor: '#4361ee',
            borderWidth: 2,
            tension: 0.3,
            pointBackgroundColor: '#4361ee',
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 5,
            fill: true
        }]
    };
    
    // خيارات الرسم البياني
    var options = {
        responsive: true,
        maintainAspectRatio: false,
        animation: false, // تعطيل الرسوم المتحركة تمامًا
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.05)'
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    };
    
    // إنشاء الرسم البياني مع بيانات الأسبوع كافتراضي
    var myChart = new Chart(chartElement, {
        type: 'line',
        data: weekData,
        options: options
    });
    
    // إضافة مستمعي الأحداث للأزرار
    var dayButton = document.querySelector('.btn-group .btn:nth-child(1)');
    var weekButton = document.querySelector('.btn-group .btn:nth-child(2)');
    var monthButton = document.querySelector('.btn-group .btn:nth-child(3)');
    
    if (dayButton) {
        dayButton.addEventListener('click', function() {
            // تعيين الزر النشط
            setActiveButton(this);
            // تحديث البيانات
            myChart.data = dayData;
            myChart.update();
        });
    }
    
    if (weekButton) {
        weekButton.addEventListener('click', function() {
            // تعيين الزر النشط
            setActiveButton(this);
            // تحديث البيانات
            myChart.data = weekData;
            myChart.update();
        });
    }
    
    if (monthButton) {
        monthButton.addEventListener('click', function() {
            // تعيين الزر النشط
            setActiveButton(this);
            // تحديث البيانات
            myChart.data = monthData;
            myChart.update();
        });
    }
    
    // دالة لتعيين الزر النشط
    function setActiveButton(button) {
        document.querySelectorAll('.btn-group .btn').forEach(function(btn) {
            btn.classList.remove('active');
        });
        button.classList.add('active');
    }
    
    // مولد الباركود
    var generateBarcodeButton = document.getElementById('generateBarcode');
    if (generateBarcodeButton) {
        generateBarcodeButton.addEventListener('click', function() {
            var value = document.getElementById('barcodeValue').value;
            if (value) {
                JsBarcode("#barcode", value, {
                    format: "CODE128",
                    lineColor: "#4361ee",
                    width: 2,
                    height: 50,
                    displayValue: true
                });
            }
        });
    }
});