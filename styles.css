body {
    background-color: #f8f9fa;
    font-family: 'Arial', sans-serif;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
}

.card-header h2 {
    color: #2c3e50;
    font-weight: 600;
}

.form-control {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 12px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    background-color: #007bff;
    border: none;
    border-radius: 8px;
    padding: 12px;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.form-check-label {
    color: #666;
}

a {
    color: #007bff;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #0056b3;
}

.text-center {
    margin-top: 1rem;
}

@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .card {
        margin: 0 15px;
    }
}