<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database connection
require_once 'db_connect.php';

// Check if the form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $fullName = $_POST['fullName'] ?? '';
    $email = $_POST['registerEmail'] ?? '';
    $password = $_POST['registerPassword'] ?? '';
    
    // Validate input
    $errors = [];
    
    if (empty($fullName)) {
        $errors[] = "الاسم الكامل مطلوب";
    }
    
    if (empty($email)) {
        $errors[] = "البريد الإلكتروني مطلوب";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "صيغة البريد الإلكتروني غير صحيحة";
    }
    
    if (empty($password)) {
        $errors[] = "كلمة المرور مطلوبة";
    } elseif (strlen($password) < 6) {
        $errors[] = "كلمة المرور يجب أن تكون على الأقل 6 أحرف";
    }
    
    // If no errors, proceed with registration
    if (empty($errors)) {
        // Make sure the users table exists
        $createTableSQL = "CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            email VARCHAR(100) NOT NULL UNIQUE,
            full_name VARCHAR(100),
            registration_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_login DATETIME,
            is_active BOOLEAN DEFAULT TRUE
        )";
        
        if (!$conn->query($createTableSQL)) {
            $response = [
                'success' => false,
                'message' => 'خطأ في إنشاء جدول المستخدمين: ' . $conn->error
            ];
            header('Content-Type: application/json');
            echo json_encode($response);
            exit;
        }
        
        // Check if email already exists
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $response = [
                'success' => false,
                'message' => 'البريد الإلكتروني مسجل بالفعل'
            ];
        } else {
            // Hash the password
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            
            // Generate username from email
            $username = explode('@', $email)[0];
            
            // Insert new user
            $insertStmt = $conn->prepare("INSERT INTO users (username, password, email, full_name) VALUES (?, ?, ?, ?)");
            $insertStmt->bind_param("ssss", $username, $hashedPassword, $email, $fullName);
            
            if ($insertStmt->execute()) {
                $response = [
                    'success' => true,
                    'message' => 'تم إنشاء الحساب بنجاح'
                ];
            } else {
                $response = [
                    'success' => false,
                    'message' => 'حدث خطأ أثناء إنشاء الحساب: ' . $insertStmt->error
                ];
            }
            $insertStmt->close();
        }
        $stmt->close();
    } else {
        $response = [
            'success' => false,
            'message' => 'يرجى تصحيح الأخطاء التالية: ' . implode(', ', $errors),
            'errors' => $errors
        ];
    }
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode($response);
    
    // Close connection
    closeConnection($conn);
    exit;
} else {
    // Not a POST request
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير صالحة'
    ]);
    exit;
}
?>