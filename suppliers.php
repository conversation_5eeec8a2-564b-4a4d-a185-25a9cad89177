<?php
// بدء جلسة
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// استيراد ملف الاتصال بقاعدة البيانات
require_once 'config/db_connection.php';

// تهيئة المتغيرات الإحصائية
$total_suppliers = 0;
$total_purchases = 0;
$unpaid_purchases = 0;
$recent_purchases = 0;

// التحقق من وجود جدول الموردين
$suppliers_table_exists_query = "SHOW TABLES LIKE 'suppliers'";
$suppliers_table_exists_result = mysqli_query($conn, $suppliers_table_exists_query);
$suppliers_table_exists = mysqli_num_rows($suppliers_table_exists_result) > 0;

// التحقق من وجود جدول المشتريات
$purchases_table_exists_query = "SHOW TABLES LIKE 'purchases'";
$purchases_table_exists_result = mysqli_query($conn, $purchases_table_exists_query);
$purchases_table_exists = mysqli_num_rows($purchases_table_exists_result) > 0;

// استعلام لجلب إحصائيات الموردين
if ($suppliers_table_exists) {
    $total_suppliers_query = "SELECT COUNT(*) as total FROM suppliers";
    $total_suppliers_result = mysqli_query($conn, $total_suppliers_query);
    if ($total_suppliers_result && mysqli_num_rows($total_suppliers_result) > 0) {
        $total_suppliers = mysqli_fetch_assoc($total_suppliers_result)['total'] ?? 0;
    }
}

if ($purchases_table_exists) {
    // إجمالي المشتريات
    $total_purchases_query = "SELECT SUM(total_amount) as total FROM purchases";
    $total_purchases_result = mysqli_query($conn, $total_purchases_query);
    if ($total_purchases_result && mysqli_num_rows($total_purchases_result) > 0) {
        $total_purchases = mysqli_fetch_assoc($total_purchases_result)['total'] ?? 0;
    }
    
    // المشتريات غير المدفوعة
    $unpaid_purchases_query = "SELECT SUM(total_amount - paid_amount) as total FROM purchases WHERE payment_status != 'paid'";
    $unpaid_purchases_result = mysqli_query($conn, $unpaid_purchases_query);
    if ($unpaid_purchases_result && mysqli_num_rows($unpaid_purchases_result) > 0) {
        $unpaid_purchases = mysqli_fetch_assoc($unpaid_purchases_result)['total'] ?? 0;
    }
    
    // المشتريات الحديثة
    $recent_purchases_query = "SELECT COUNT(*) as total FROM purchases WHERE purchase_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
    $recent_purchases_result = mysqli_query($conn, $recent_purchases_query);
    if ($recent_purchases_result && mysqli_num_rows($recent_purchases_result) > 0) {
        $recent_purchases = mysqli_fetch_assoc($recent_purchases_result)['total'] ?? 0;
    }
}

// معالجة البحث والفلترة
$search = isset($_GET['search']) ? $_GET['search'] : '';

// تهيئة متغير نتيجة الموردين
$suppliers_result = false;

// بناء استعلام الموردين إذا كان الجدول موجودًا
if ($suppliers_table_exists) {
    if ($purchases_table_exists) {
        $suppliers_query = "SELECT s.*, 
                        (SELECT COUNT(*) FROM purchases WHERE supplier_id = s.id) as purchases_count,
                        (SELECT SUM(total_amount) FROM purchases WHERE supplier_id = s.id) as total_purchases,
                        (SELECT SUM(total_amount - paid_amount) FROM purchases WHERE supplier_id = s.id AND payment_status != 'paid') as unpaid_amount
                        FROM suppliers s
                        WHERE 1=1";
    } else {
        $suppliers_query = "SELECT s.*, 
                        0 as purchases_count,
                        0 as total_purchases,
                        0 as unpaid_amount
                        FROM suppliers s
                        WHERE 1=1";
    }
    
    if (!empty($search)) {
        $suppliers_query .= " AND (s.name LIKE '%$search%' OR s.phone LIKE '%$search%' OR s.email LIKE '%$search%')";
    }
    
    $suppliers_query .= " ORDER BY s.name";
    $suppliers_result = mysqli_query($conn, $suppliers_query);
}

// معالجة إضافة مورد جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_supplier'])) {
    $name = mysqli_real_escape_string($conn, $_POST['name']);
    $phone = mysqli_real_escape_string($conn, $_POST['phone']);
    $email = !empty($_POST['email']) ? mysqli_real_escape_string($conn, $_POST['email']) : null;
    $address = mysqli_real_escape_string($conn, $_POST['address']);
    $contact_person = mysqli_real_escape_string($conn, $_POST['contact_person']);
    $notes = mysqli_real_escape_string($conn, $_POST['notes']);
    
    // التحقق من وجود جدول الموردين
    if (!$suppliers_table_exists) {
        // إنشاء جدول الموردين إذا لم يكن موجودًا
        $create_suppliers_table_query = "CREATE TABLE suppliers (
            id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            phone VARCHAR(20) NULL,
            email VARCHAR(100) NULL,
            address TEXT NULL,
            contact_person VARCHAR(100) NULL,
            notes TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        if (mysqli_query($conn, $create_suppliers_table_query)) {
            $suppliers_table_exists = true;
        } else {
            $error_message = "فشل في إنشاء جدول الموردين: " . mysqli_error($conn);
        }
    }
    
    if ($suppliers_table_exists) {
        $insert_query = "INSERT INTO suppliers (name, phone, email, address, contact_person, notes) 
                        VALUES ('$name', '$phone', " . ($email ? "'$email'" : "NULL") . ", '$address', '$contact_person', '$notes')";
        
        if (mysqli_query($conn, $insert_query)) {
            $success_message = "تم إضافة المورد بنجاح.";
            // إعادة تحميل الصفحة لتحديث البيانات
            header("Location: suppliers.php?success=1");
            exit();
        } else {
            $error_message = "حدث خطأ أثناء إضافة المورد: " . mysqli_error($conn);
        }
    }
}

// معالجة تعديل مورد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['edit_supplier']) && $suppliers_table_exists) {
    $supplier_id = intval($_POST['supplier_id']);
    $name = mysqli_real_escape_string($conn, $_POST['name']);
    $phone = mysqli_real_escape_string($conn, $_POST['phone']);
    $email = !empty($_POST['email']) ? mysqli_real_escape_string($conn, $_POST['email']) : null;
    $address = mysqli_real_escape_string($conn, $_POST['address']);
    $contact_person = mysqli_real_escape_string($conn, $_POST['contact_person']);
    $notes = mysqli_real_escape_string($conn, $_POST['notes']);
    
    $update_query = "UPDATE suppliers SET 
                    name = '$name', 
                    phone = '$phone', 
                    email = " . ($email ? "'$email'" : "NULL") . ", 
                    address = '$address', 
                    contact_person = '$contact_person', 
                    notes = '$notes' 
                    WHERE id = $supplier_id";
    
    if (mysqli_query($conn, $update_query)) {
        $success_message = "تم تحديث المورد بنجاح.";
        // إعادة تحميل الصفحة لتحديث البيانات
        header("Location: suppliers.php?success=2");
        exit();
    } else {
        $error_message = "حدث خطأ أثناء تحديث المورد: " . mysqli_error($conn);
    }
}

// معالجة حذف مورد
if (isset($_GET['delete']) && !empty($_GET['delete']) && $suppliers_table_exists) {
    $supplier_id = intval($_GET['delete']);
    
    // التحقق من عدم وجود مشتريات مرتبطة بالمورد
    $purchases_count = 0;
    if ($purchases_table_exists) {
        $check_purchases_query = "SELECT COUNT(*) as count FROM purchases WHERE supplier_id = $supplier_id";
        $check_purchases_result = mysqli_query($conn, $check_purchases_query);
        if ($check_purchases_result && mysqli_num_rows($check_purchases_result) > 0) {
            $purchases_count = mysqli_fetch_assoc($check_purchases_result)['count'];
        }
    }
    
    if ($purchases_count > 0) {
        $error_message = "لا يمكن حذف المورد لأنه مرتبط بمشتريات سابقة.";
    } else {
        $delete_query = "DELETE FROM suppliers WHERE id = $supplier_id";
        if (mysqli_query($conn, $delete_query)) {
            $success_message = "تم حذف المورد بنجاح.";
            // إعادة تحميل الصفحة لتحديث البيانات
            header("Location: suppliers.php?success=3");
            exit();
        } else {
            $error_message = "حدث خطأ أثناء حذف المورد: " . mysqli_error($conn);
        }
    }
}

// معالجة إضافة دفعة للمورد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_payment'])) {
    $supplier_id = intval($_POST['supplier_id']);
    $amount = floatval($_POST['amount']);
    $payment_date = mysqli_real_escape_string($conn, $_POST['payment_date']);
    $payment_method = mysqli_real_escape_string($conn, $_POST['payment_method']);
    $notes = mysqli_real_escape_string($conn, $_POST['notes']);
    
    // إضافة الدفعة إلى جدول المدفوعات (يجب إنشاء هذا الجدول)
    $insert_payment_query = "INSERT INTO supplier_payments (supplier_id, amount, payment_date, payment_method, notes) 
                            VALUES ($supplier_id, $amount, '$payment_date', '$payment_method', '$notes')";
    
    if (mysqli_query($conn, $insert_payment_query)) {
        // تحديث حالة الدفع في المشتريات المرتبطة بالمورد
        // هذا مجرد مثال، قد تحتاج إلى تعديله حسب هيكل قاعدة البيانات الخاصة بك
        $update_purchases_query = "UPDATE purchases 
                                  SET paid_amount = CASE 
                                      WHEN paid_amount + $amount >= total_amount THEN total_amount 
                                      ELSE paid_amount + $amount 
                                  END,
                                  payment_status = CASE 
                                      WHEN paid_amount + $amount >= total_amount THEN 'paid' 
                                      ELSE 'partial' 
                                  END 
                                  WHERE supplier_id = $supplier_id AND payment_status != 'paid' 
                                  ORDER BY purchase_date ASC 
                                  LIMIT 1";
        
        mysqli_query($conn, $update_purchases_query);
        
        $success_message = "تم إضافة الدفعة بنجاح.";
        // إعادة تحميل الصفحة لتحديث البيانات
        header("Location: suppliers.php?success=4");
        exit();
    } else {
        $error_message = "حدث خطأ أثناء إضافة الدفعة: " . mysqli_error($conn);
    }
}

// رسائل النجاح
if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case 1:
            $success_message = "تم إضافة المورد بنجاح.";
            break;
        case 2:
            $success_message = "تم تحديث المورد بنجاح.";
            break;
        case 3:
            $success_message = "تم حذف المورد بنجاح.";
            break;
        case 4:
            $success_message = "تم إضافة الدفعة بنجاح.";
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموردين - نظام إدارة المتجر</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --sidebar-width: 250px;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fb;
            color: #333;
            overflow-x: hidden;
        }
        
        /* تصميم الشريط الجانبي */
        .sidebar {
            height: 100vh;
            width: var(--sidebar-width);
            position: fixed;
            top: 0;
            right: 0;
            background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding-top: 20px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            z-index: 1000;
        }
        
        .sidebar .logo {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .logo h3 {
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 15px;
            border-radius: 8px;
            transition: all 0.3s;
            font-size: 16px;
        }
        
        .sidebar .nav-link:hover, 
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar .nav-link i {
            margin-left: 10px;
            width: 25px;
            text-align: center;
        }
        
        /* تصميم المحتوى الرئيسي */
        .main-content {
            margin-right: var(--sidebar-width);
            padding: 20px;
            transition: all 0.3s;
        }
        
        /* شريط التنقل العلوي */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        
        .search-bar {
            background-color: #f8f9fa;
            border-radius: 30px;
            padding: 8px 20px;
            border: 1px solid #eaeaea;
        }
        
        .search-bar input {
            background: transparent;
            border: none;
            width: 100%;
            padding: 5px 10px;
        }
        
        .search-bar input:focus {
            outline: none;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #f94144;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* بطاقات الإحصائيات */
        .stat-card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .stat-card .card-title {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-card .card-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .stat-card .card-footer {
            background: transparent;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 13px;
            padding: 8px 15px;
        }
        
        /* تصميم قسم الموردين */
        .suppliers-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .supplier-filters {
            margin-bottom: 20px;
        }
        
        .supplier-table .supplier-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .supplier-table .supplier-status.active {
            background-color: #e6f4ea;
            color: #0f9d58;
        }
        
        .supplier-table .supplier-status.inactive {
            background-color: #fce8e6;
            color: #ea4335;
        }
        
        .supplier-actions .btn {
            padding: 5px 10px;
            font-size: 14px;
        }
        
        /* تصميم النموذج */
        .modal-header {
            background-color: var(--primary-color);
            color: white;
        }
        
        .modal-footer {
            background-color: #f8f9fa;
        }
        
        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
                overflow: hidden;
            }
            
            .sidebar .logo h3, .sidebar .nav-link span {
                display: none;
            }
            
            .sidebar .nav-link {
                text-align: center;
                margin: 5px 10px;
            }
            
            .sidebar .nav-link i {
                margin-left: 0;
            }
            
            .main-content {
                margin-right: 70px;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-store"></i> متجرنا</h3>
        </div>
        <ul class="nav flex-column mt-4">
            <li class="nav-item">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales.php">
                    <i class="fas fa-shopping-cart"></i>
                    <span>المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales_management.php">
                    <i class="fas fa-chart-line"></i>
                    <span>إدارة المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="products.php">
                    <i class="fas fa-box"></i>
                    <span>المنتجات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customers.php">
                    <i class="fas fa-users"></i>
                    <span>العملاء</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="suppliers.php">
                    <i class="fas fa-truck"></i>
                    <span>الموردين</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="purchases.php">
                    <i class="fas fa-shopping-basket"></i>
                    <span>المشتريات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="expenses.php">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>المصروفات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="cashbox.php">
                    <i class="fas fa-cash-register"></i>
                    <span>الخزنة</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="financial.php">
                    <i class="fas fa-chart-pie"></i>
                    <span>الإدارة المالية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="barcode.php">
                    <i class="fas fa-barcode"></i>
                    <span>مولد الباركود</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="settings.php">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط التنقل العلوي -->
        <div class="top-navbar">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4 class="mb-0">إدارة الموردين</h4>
                    <p class="mb-0 text-muted">مرحباً بعودتك، <?php echo $_SESSION['username']; ?>!</p>
                </div>
                <div class="col-md-6 d-flex justify-content-end">
                    <div class="d-flex align-items-center">
                        <div class="search-bar me-3">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="بحث...">
                        </div>
                        <div class="position-relative me-3">
                            <button class="btn btn-light rounded-circle">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-light rounded-circle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.php">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- عرض رسائل النجاح والخطأ -->
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <!-- بطاقات الإحصائيات -->
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-primary-subtle text-primary me-3">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div>
                            <div class="card-title">إجمالي الموردين</div>
                            <div class="card-value"><?php echo number_format($total_suppliers); ?></div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-sync-alt"></i> آخر تحديث: <?php echo date('Y-m-d H:i'); ?>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-success-subtle text-success me-3">
                            <i class="fas fa-shopping-basket"></i>
                        </div>
                        <div>
                            <div class="card-title">إجمالي المشتريات</div>
                            <div class="card-value"><?php echo number_format($total_purchases, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-info-circle"></i> القيمة الإجمالية للمشتريات
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-danger-subtle text-danger me-3">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div>
                            <div class="card-title">المبالغ المستحقة</div>
                            <div class="card-value"><?php echo number_format($unpaid_purchases, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-exclamation-circle"></i> مبالغ غير مدفوعة للموردين
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-warning-subtle text-warning me-3">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div>
                            <div class="card-title">مشتريات حديثة</div>
                            <div class="card-value"><?php echo number_format($recent_purchases); ?></div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-clock"></i> آخر 30 يوم
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الموردين -->
        <div class="suppliers-container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0">قائمة الموردين</h5>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSupplierModal">
                        <i class="fas fa-plus me-1"></i> إضافة مورد
                    </button>
                </div>
            </div>
            
            <!-- فلاتر البحث -->
            <div class="supplier-filters">
                <form action="" method="get" class="row g-3">
                    <div class="col-md-8">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" name="search" placeholder="بحث باسم المورد أو رقم الهاتف أو البريد الإلكتروني" value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary w-100">بحث</button>
                    </div>
                </form>
            </div>
            
            <!-- جدول الموردين -->
            <div class="supplier-table">
                <div class="table-responsive">
                    <table class="table table-hover" id="suppliersTable">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم المورد</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>جهة الاتصال</th>
                                <th>عدد المشتريات</th>
                                <th>إجمالي المشتريات</th>
                                <th>المبالغ المستحقة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            if ($suppliers_result && mysqli_num_rows($suppliers_result) > 0) {
                                $counter = 1;
                                while ($supplier = mysqli_fetch_assoc($suppliers_result)) {
                                    echo '<tr>';
                                    echo '<td>' . $counter++ . '</td>';
                                    echo '<td>' . htmlspecialchars($supplier['name']) . '</td>';
                                    echo '<td>' . htmlspecialchars($supplier['phone']) . '</td>';
                                    echo '<td>' . (!empty($supplier['email']) ? htmlspecialchars($supplier['email']) : '-') . '</td>';
                                    echo '<td>' . htmlspecialchars($supplier['contact_person']) . '</td>';
                                    echo '<td>' . number_format($supplier['purchases_count']) . '</td>';
                                    echo '<td>' . number_format($supplier['total_purchases'] ?? 0, 2) . ' ر.س</td>';
                                    echo '<td>' . number_format($supplier['unpaid_amount'] ?? 0, 2) . ' ر.س</td>';
                                    echo '<td class="supplier-actions">';
                                    echo '<button class="btn btn-sm btn-info me-1" onclick="editSupplier(' . $supplier['id'] . ')" data-bs-toggle="modal" data-bs-target="#editSupplierModal"><i class="fas fa-edit"></i></button>';
                                    
                                    if ($supplier['unpaid_amount'] > 0) {
                                        echo '<button class="btn btn-sm btn-success me-1" onclick="addPayment(' . $supplier['id'] . ', \'' . htmlspecialchars($supplier['name']) . '\', ' . $supplier['unpaid_amount'] . ')" data-bs-toggle="modal" data-bs-target="#addPaymentModal"><i class="fas fa-money-bill-wave"></i></button>';
                                    }
                                    
                                    echo '<a href="supplier_details.php?id=' . $supplier['id'] . '" class="btn btn-sm btn-primary me-1"><i class="fas fa-eye"></i></a>';
                                    
                                    if ($supplier['purchases_count'] == 0) {
                                        echo '<button class="btn btn-sm btn-danger" onclick="confirmDelete(' . $supplier['id'] . ')"><i class="fas fa-trash"></i></button>';
                                    }
                                    
                                    echo '</td>';
                                    echo '</tr>';
                                }
                            } else {
                                echo '<tr><td colspan="9" class="text-center">لا يوجد موردين متاحين</td></tr>';
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال إضافة مورد جديد -->
    <div class="modal fade" id="addSupplierModal" tabindex="-1" aria-labelledby="addSupplierModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addSupplierModalLabel">إضافة مورد جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addSupplierForm" action="" method="post">
                        <input type="hidden" name="add_supplier" value="1">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">اسم المورد <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-6">
                                <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="phone" name="phone" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                            <div class="col-md-6">
                                <label for="contact_person" class="form-label">جهة الاتصال</label>
                                <input type="text" class="form-control" id="contact_person" name="contact_person">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="addSupplierForm" class="btn btn-primary">إضافة المورد</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال تعديل مورد -->
    <div class="modal fade" id="editSupplierModal" tabindex="-1" aria-labelledby="editSupplierModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editSupplierModalLabel">تعديل مورد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editSupplierForm" action="" method="post">
                        <input type="hidden" name="edit_supplier" value="1">
                        <input type="hidden" name="supplier_id" id="edit_supplier_id">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_name" class="form-label">اسم المورد <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_name" name="name" required>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_phone" name="phone" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="edit_email" name="email">
                            </div>
                            <div class="col-md-6">
                                <label for="edit_contact_person" class="form-label">جهة الاتصال</label>
                                <input type="text" class="form-control" id="edit_contact_person" name="contact_person">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="edit_address" name="address" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="edit_notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="edit_notes" name="notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="editSupplierForm" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال إضافة دفعة -->
    <div class="modal fade" id="addPaymentModal" tabindex="-1" aria-labelledby="addPaymentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addPaymentModalLabel">إضافة دفعة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addPaymentForm" action="" method="post">
                        <input type="hidden" name="add_payment" value="1">
                        <input type="hidden" name="supplier_id" id="payment_supplier_id">
                        <div class="mb-3">
                            <label for="supplier_name" class="form-label">اسم المورد</label>
                            <input type="text" class="form-control" id="supplier_name" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="unpaid_amount" class="form-label">المبلغ المستحق</label>
                            <input type="text" class="form-control" id="unpaid_amount" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="amount" class="form-label">مبلغ الدفعة <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0" required>
                        </div>
                        <div class="mb-3">
                            <label for="payment_date" class="form-label">تاريخ الدفع <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="payment_date" name="payment_date" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="payment_method" class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                            <select class="form-select" id="payment_method" name="payment_method" required>
                                <option value="cash">نقدي</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="payment_notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="payment_notes" name="notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="addPaymentForm" class="btn btn-primary">إضافة الدفعة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        // تهيئة جدول الموردين
        $(document).ready(function() {
            try {
                $('#suppliersTable').DataTable({
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
                    },
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "الكل"]],
                    "columnDefs": [
                        { "orderable": false, "targets": 8 } // جعل عمود الإجراءات غير قابل للترتيب
                    ],
                    "retrieve": true,
                    "paging": true,
                    "ordering": true,
                    "info": true,
                    "autoWidth": false,
                    "searching": true,
                    "responsive": true
                });
            } catch (e) {
                console.error("Error initializing DataTable:", e);
                // Fallback a una tabla básica si DataTables falla
                $('#suppliersTable').addClass('table-striped');
            }
        });
        
        // دالة لتعديل المورد
        function editSupplier(supplierId) {
            // هنا يمكن إضافة كود AJAX لجلب بيانات المورد من الخادم
            // لأغراض العرض، سنستخدم بيانات وهمية
            
            // في التطبيق الحقيقي، يجب استبدال هذا بطلب AJAX
            fetch('get_supplier.php?id=' + supplierId)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('edit_supplier_id').value = data.id;
                    document.getElementById('edit_name').value = data.name;
                    document.getElementById('edit_phone').value = data.phone;
                    document.getElementById('edit_email').value = data.email || '';
                    document.getElementById('edit_contact_person').value = data.contact_person;
                    document.getElementById('edit_address').value = data.address;
                    document.getElementById('edit_notes').value = data.notes;
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء جلب بيانات المورد');
                });
        }
        
        // دالة لإضافة دفعة
        function addPayment(supplierId, supplierName, unpaidAmount) {
            document.getElementById('payment_supplier_id').value = supplierId;
            document.getElementById('supplier_name').value = supplierName;
            document.getElementById('unpaid_amount').value = unpaidAmount.toFixed(2) + ' ر.س';
            document.getElementById('amount').value = unpaidAmount.toFixed(2);
            document.getElementById('amount').max = unpaidAmount;
        }
        
        // دالة لتأكيد حذف المورد
        function confirmDelete(supplierId) {
            if (confirm('هل أنت متأكد من رغبتك في حذف هذا المورد؟')) {
                window.location.href = 'suppliers.php?delete=' + supplierId;
            }
        }
    </script>
</body>
</html>