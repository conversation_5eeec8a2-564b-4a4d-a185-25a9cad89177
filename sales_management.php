<?php
// بدء جلسة
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// استيراد ملف الاتصال بقاعدة البيانات
require_once 'config/db_connection.php';

// استعلام لجلب إحصائيات المبيعات
$today = date('Y-m-d');
$month_start = date('Y-m-01');
$month_end = date('Y-m-t');

// مبيعات اليوم
$today_sales_query = "SELECT COUNT(*) as count, SUM(total_amount) as total FROM invoices WHERE DATE(created_at) = '$today'";
$today_sales_result = mysqli_query($conn, $today_sales_query);
$today_sales = mysqli_fetch_assoc($today_sales_result);

// مبيعات الشهر
$month_sales_query = "SELECT COUNT(*) as count, SUM(total_amount) as total FROM invoices WHERE created_at BETWEEN '$month_start' AND '$month_end'";
$month_sales_result = mysqli_query($conn, $month_sales_query);
$month_sales = mysqli_fetch_assoc($month_sales_result);

// إجمالي المبيعات
$total_sales_query = "SELECT COUNT(*) as count, SUM(total_amount) as total FROM invoices";
$total_sales_result = mysqli_query($conn, $total_sales_query);
$total_sales = mysqli_fetch_assoc($total_sales_result);

// الديون المستحقة
$debts_query = "SELECT SUM(debt_balance) as total FROM customers WHERE debt_balance > 0";
$debts_result = mysqli_query($conn, $debts_query);
$debts = mysqli_fetch_assoc($debts_result);

// استعلام لجلب آخر 10 فواتير
$recent_invoices_query = "SELECT i.*, c.name as customer_name 
                         FROM invoices i 
                         LEFT JOIN customers c ON i.customer_id = c.id 
                         ORDER BY i.created_at DESC 
                         LIMIT 10";
$recent_invoices_result = mysqli_query($conn, $recent_invoices_query);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المبيعات - نظام إدارة المتجر</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- ApexCharts -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --whatsapp-color: #25D366;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --sidebar-width: 250px;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fb;
            color: #333;
            overflow-x: hidden;
        }
        
        /* تصميم الشريط الجانبي */
        .sidebar {
            height: 100vh;
            width: var(--sidebar-width);
            position: fixed;
            top: 0;
            right: 0;
            background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding-top: 20px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            z-index: 1000;
        }
        
        .sidebar .logo {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .logo h3 {
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 15px;
            border-radius: 8px;
            transition: all 0.3s;
            font-size: 16px;
        }
        
        .sidebar .nav-link:hover, 
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar .nav-link i {
            margin-left: 10px;
            width: 25px;
            text-align: center;
        }
        
        /* تصميم المحتوى الرئيسي */
        .main-content {
            margin-right: var(--sidebar-width);
            padding: 20px;
            transition: all 0.3s;
        }
        
        /* شريط التنقل العلوي */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        
        .search-bar {
            background-color: #f8f9fa;
            border-radius: 30px;
            padding: 8px 20px;
            border: 1px solid #eaeaea;
        }
        
        .search-bar input {
            background: transparent;
            border: none;
            width: 100%;
            padding: 5px 10px;
        }
        
        .search-bar input:focus {
            outline: none;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #f94144;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* أزرار العمل السريعة */
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .action-btn {
            flex: 1;
            min-width: 200px;
            padding: 15px;
            border-radius: 12px;
            background: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border: none;
            text-align: center;
            transition: all 0.3s;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .action-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }
        
        .action-btn i {
            font-size: 32px;
            margin-bottom: 10px;
        }
        
        .action-btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }
        
        .action-btn-whatsapp {
            background: linear-gradient(135deg, var(--whatsapp-color), #128C7E);
            color: white;
        }
        
        .action-btn-success {
            background: linear-gradient(135deg, var(--success-color), #1e7e34);
            color: white;
        }
        
        .action-btn-info {
            background: linear-gradient(135deg, var(--info-color), #138496);
            color: white;
        }
        
        /* بطاقات الإحصائيات */
        .stat-card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .stat-card .card-title {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-card .card-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .stat-card .card-footer {
            background: transparent;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 13px;
            padding: 8px 15px;
        }
        
        /* الرسوم البيانية */
        .chart-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        /* جدول المبيعات */
        .sales-table {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .sales-table table {
            width: 100%;
        }
        
        .sales-table .badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 500;
        }
        
        .sales-table .status-completed {
            background-color: #e6f4ea;
            color: #0f9d58;
        }
        
        .sales-table .status-pending {
            background-color: #fef7e0;
            color: #f9ab00;
        }
        
        .sales-table .status-canceled {
            background-color: #fce8e6;
            color: #ea4335;
        }
        
        /* فلترة المبيعات */
        .filters-section {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        /* تفاصيل الفاتورة */
        .invoice-preview {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
        }
        
        .invoice-header {
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        
        .invoice-items {
            margin-bottom: 30px;
        }
        
        .invoice-summary {
            border-top: 2px solid #eee;
            padding-top: 20px;
        }
        
        .invoice-actions {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn-whatsapp {
            background-color: var(--whatsapp-color);
            color: white;
            border-color: var(--whatsapp-color);
        }
        
        .btn-whatsapp:hover {
            background-color: #128C7E;
            color: white;
        }
        
        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
                overflow: hidden;
            }
            
            .sidebar .logo h3, .sidebar .nav-link span {
                display: none;
            }
            
            .sidebar .nav-link {
                text-align: center;
                margin: 5px 10px;
            }
            
            .sidebar .nav-link i {
                margin-left: 0;
            }
            
            .main-content {
                margin-right: 70px;
            }
            
            .action-btn {
                min-width: 100%;
            }
            
            .invoice-actions .btn {
                flex: 1;
                min-width: 100%;
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-store"></i> متجرنا</h3>
        </div>
        <ul class="nav flex-column mt-4">
            <li class="nav-item">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales.php">
                    <i class="fas fa-shopping-cart"></i>
                    <span>المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="sales_management.php">
                    <i class="fas fa-chart-line"></i>
                    <span>إدارة المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="products.php">
                    <i class="fas fa-box"></i>
                    <span>المنتجات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customers.php">
                    <i class="fas fa-users"></i>
                    <span>العملاء</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="suppliers.php">
                    <i class="fas fa-truck"></i>
                    <span>الموردين</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="purchases.php">
                    <i class="fas fa-shopping-basket"></i>
                    <span>المشتريات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="expenses.php">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>المصروفات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="financial.php">
                    <i class="fas fa-chart-pie"></i>
                    <span>الإدارة المالية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="barcode.php">
                    <i class="fas fa-barcode"></i>
                    <span>مولد الباركود</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="settings.php">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط التنقل العلوي -->
        <div class="top-navbar">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4 class="mb-0">إدارة المبيعات</h4>
                    <p class="mb-0 text-muted">مرحباً بعودتك، <?php echo $_SESSION['username']; ?>!</p>
                </div>
                <div class="col-md-6 d-flex justify-content-end">
                    <div class="d-flex align-items-center">
                        <div class="search-bar me-3">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="بحث...">
                        </div>
                        <div class="position-relative me-3">
                            <button class="btn btn-light rounded-circle">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-light rounded-circle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.php">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار العمل السريعة -->
        <div class="action-buttons">
            <a href="sales.php" class="action-btn action-btn-primary">
                <i class="fas fa-shopping-cart"></i>
                <span>نقطة البيع</span>
            </a>
            <a href="#" class="action-btn action-btn-success">
                <i class="fas fa-file-invoice"></i>
                <span>إنشاء فاتورة</span>
            </a>
            <a href="#" class="action-btn action-btn-info">
                <i class="fas fa-file-export"></i>
                <span>تصدير التقارير</span>
            </a>
            <a href="#" class="action-btn action-btn-whatsapp">
                <i class="fab fa-whatsapp"></i>
                <span>إرسال عبر واتساب</span>
            </a>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="row">
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-primary-subtle text-primary me-3">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div>
                            <div class="card-title">مبيعات اليوم</div>
                            <div class="card-value"><?php echo number_format($today_sales['total'] ?? 0, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-receipt"></i> <?php echo $today_sales['count'] ?? 0; ?> فاتورة
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-success-subtle text-success me-3">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div>
                            <div class="card-title">مبيعات الشهر</div>
                            <div class="card-value"><?php echo number_format($month_sales['total'] ?? 0, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-receipt"></i> <?php echo $month_sales['count'] ?? 0; ?> فاتورة
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-info-subtle text-info me-3">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div>
                            <div class="card-title">إجمالي المبيعات</div>
                            <div class="card-value"><?php echo number_format($total_sales['total'] ?? 0, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-receipt"></i> <?php echo $total_sales['count'] ?? 0; ?> فاتورة
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-danger-subtle text-danger me-3">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <div>
                            <div class="card-title">الديون المستحقة</div>
                            <div class="card-value"><?php echo number_format($debts['total'] ?? 0, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <a href="customers.php?filter=debt" class="text-decoration-none">عرض العملاء المدينين</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- الرسم البياني للمبيعات -->
            <div class="col-md-8">
                <div class="chart-container">
                    <div class="chart-header">
                        <h5 class="mb-0">تحليل المبيعات</h5>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-secondary">اليوم</button>
                            <button class="btn btn-sm btn-outline-secondary active">الأسبوع</button>
                            <button class="btn btn-sm btn-outline-secondary">الشهر</button>
                        </div>
                    </div>
                    <div id="salesChart" style="height: 300px;"></div>
                </div>
            </div>
            
            <!-- الرسم البياني الدائري لطرق الدفع -->
            <div class="col-md-4">
                <div class="chart-container">
                    <div class="chart-header">
                        <h5 class="mb-0">طرق الدفع</h5>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="paymentDropdown" data-bs-toggle="dropdown">
                                الشهر الحالي
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="paymentDropdown">
                                <li><a class="dropdown-item" href="#">الشهر الحالي</a></li>
                                <li><a class="dropdown-item" href="#">الربع الحالي</a></li>
                                <li><a class="dropdown-item" href="#">السنة الحالية</a></li>
                            </ul>
                        </div>
                    </div>
                    <div id="paymentMethodsChart" style="height: 300px;"></div>
                </div>
            </div>
        </div>

        <!-- فلترة المبيعات -->
        <div class="filters-section">
            <h5 class="mb-3">فلترة المبيعات</h5>
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="dateRange" class="form-label">الفترة الزمنية</label>
                    <select class="form-select" id="dateRange">
                        <option value="today">اليوم</option>
                        <option value="yesterday">الأمس</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month" selected>هذا الشهر</option>
                        <option value="custom">تاريخ مخصص</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="paymentMethod" class="form-label">طريقة الدفع</label>
                    <select class="form-select" id="paymentMethod">
                        <option value="all" selected>الكل</option>
                        <option value="cash">نقدي</option>
                        <option value="card">بطاقة</option>
                        <option value="credit">آجل</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="customerFilter" class="form-label">العميل</label>
                    <select class="form-select" id="customerFilter">
                        <option value="all" selected>الكل</option>
                        <?php
                        // استعلام لجلب العملاء
                        $customers_query = "SELECT id, name FROM customers ORDER BY name ASC";
                        $customers_result = mysqli_query($conn, $customers_query);
                        
                        while ($customer = mysqli_fetch_assoc($customers_result)) {
                            echo '<option value="' . $customer['id'] . '">' . $customer['name'] . '</option>';
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="invoiceStatus" class="form-label">حالة الفاتورة</label>
                    <select class="form-select" id="invoiceStatus">
                        <option value="all" selected>الكل</option>
                        <option value="completed">مكتملة</option>
                        <option value="pending">معلقة</option>
                        <option value="canceled">ملغية</option>
                    </select>
                </div>
                <div class="col-12">
                    <button class="btn btn-primary">
                        <i class="fas fa-filter me-2"></i> تطبيق الفلتر
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="fas fa-redo me-2"></i> إعادة تعيين
                    </button>
                </div>
            </div>
        </div>

        <!-- جدول المبيعات -->
        <div class="sales-table">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0">آخر المبيعات</h5>
                <a href="#" class="btn btn-sm btn-primary">عرض الكل</a>
            </div>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // عرض آخر الفواتير
                        if (mysqli_num_rows($recent_invoices_result) > 0) {
                            while ($invoice = mysqli_fetch_assoc($recent_invoices_result)) {
                                // تحديد حالة الفاتورة
                                $status_class = '';
                                $status_text = '';
                                
                                if ($invoice['payment_type'] == 'cash' || $invoice['payment_type'] == 'card') {
                                    $status_class = 'status-completed';
                                    $status_text = 'مكتملة';
                                } elseif ($invoice['payment_type'] == 'credit') {
                                    if ($invoice['paid_amount'] < $invoice['total_amount']) {
                                        $status_class = 'status-pending';
                                        $status_text = 'معلقة';
                                    } else {
                                        $status_class = 'status-completed';
                                        $status_text = 'مكتملة';
                                    }
                                }
                                
                                // تحديد طريقة الدفع
                                $payment_method = '';
                                switch ($invoice['payment_type']) {
                                    case 'cash':
                                        $payment_method = 'نقدي';
                                        break;
                                    case 'card':
                                        $payment_method = 'بطاقة';
                                        break;
                                    case 'credit':
                                        $payment_method = 'آجل';
                                        break;
                                    default:
                                        $payment_method = $invoice['payment_type'];
                                }
                                
                                echo '<tr>';
                                echo '<td>#' . $invoice['invoice_id'] . '</td>';
                                echo '<td>' . ($invoice['customer_name'] ?? 'عميل غير مسجل') . '</td>';
                                echo '<td>' . date('Y-m-d H:i', strtotime($invoice['created_at'])) . '</td>';
                                echo '<td>' . number_format($invoice['total_amount'], 2) . ' ر.س</td>';
                                echo '<td>' . $payment_method . '</td>';
                                echo '<td><span class="badge ' . $status_class . '">' . $status_text . '</span></td>';
                                echo '<td>';
                                echo '<div class="btn-group">';
                                echo '<button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#invoiceModal" data-invoice-id="' . $invoice['invoice_id'] . '"><i class="fas fa-eye"></i></button>';
                                echo '<button class="btn btn-sm btn-outline-success"><i class="fas fa-print"></i></button>';
                                echo '<button class="btn btn-sm btn-outline-info"><i class="fab fa-whatsapp"></i></button>';
                                echo '</div>';
                                echo '</td>';
                                echo '</tr>';
                            }
                        } else {
                            echo '<tr><td colspan="7" class="text-center">لا توجد فواتير</td></tr>';
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- مودال تفاصيل الفاتورة -->
    <div class="modal fade" id="invoiceModal" tabindex="-1" aria-labelledby="invoiceModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="invoiceModalLabel">تفاصيل الفاتورة #12345</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="invoice-preview">
                        <div class="invoice-header">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>فاتورة مبيعات</h5>
                                    <p class="mb-1">رقم الفاتورة: #12345</p>
                                    <p class="mb-1">التاريخ: 2023-08-15 14:30</p>
                                    <p>طريقة الدفع: نقدي</p>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <h5>بيانات العميل</h5>
                                    <p class="mb-1">الاسم: أحمد محمد</p>
                                    <p class="mb-1">الهاتف: 0512345678</p>
                                    <p>البريد الإلكتروني: <EMAIL></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="invoice-items">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>المنتج</th>
                                        <th>السعر</th>
                                        <th>الكمية</th>
                                        <th>الإجمالي</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1</td>
                                        <td>هاتف ذكي</td>
                                        <td>1,200.00 ر.س</td>
                                        <td>1</td>
                                        <td>1,200.00 ر.س</td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>سماعات لاسلكية</td>
                                        <td>150.00 ر.س</td>
                                        <td>2</td>
                                        <td>300.00 ر.س</td>
                                    </tr>
                                    <tr>
                                        <td>3</td>
                                        <td>حافظة هاتف</td>
                                        <td>50.00 ر.س</td>
                                        <td>1</td>
                                        <td>50.00 ر.س</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="invoice-summary">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <h6>معلومات الدفع</h6>
                                        <p class="mb-1">المبلغ المدفوع: 1,600.00 ر.س</p>
                                        <p class="mb-1">المتبقي للعميل: 0.00 ر.س</p>
                                        <p>الرصيد المحفوظ: 50.00 ر.س</p>
                                    </div>
                                    <div>
                                        <h6>ملاحظات</h6>
                                        <p>تم استخدام 50 ريال من الرصيد المحفوظ للعميل</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td>المجموع:</td>
                                            <td class="text-end">1,550.00 ر.س</td>
                                        </tr>
                                        <tr>
                                            <td>الضريبة (15%):</td>
                                            <td class="text-end">232.50 ر.س</td>
                                        </tr>
                                        <tr>
                                            <td>الخصم:</td>
                                            <td class="text-end">50.00 ر.س</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">الإجمالي:</td>
                                            <td class="text-end fw-bold">1,732.50 ر.س</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="invoice-actions w-100">
                        <button class="btn btn-primary">
                            <i class="fas fa-print me-2"></i> طباعة
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-file-pdf me-2"></i> تصدير PDF
                        </button>
                        <button class="btn btn-whatsapp">
                            <i class="fab fa-whatsapp me-2"></i> إرسال عبر واتساب
                        </button>
                        <button class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i> إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // رسم بياني للمبيعات
            var salesChartOptions = {
                series: [{
                    name: 'المبيعات',
                    data: [3200, 2800, 4500, 3800, 5200, 4900, 6000]
                }],
                chart: {
                    height: 300,
                    type: 'area',
                    fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
                    toolbar: {
                        show: false
                    }
                },
                colors: ['#4361ee'],
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth',
                    width: 2
                },
                fill: {
                    type: 'gradient',
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.7,
                        opacityTo: 0.2,
                        stops: [0, 90, 100]
                    }
                },
                xaxis: {
                    categories: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة']
                },
                yaxis: {
                    labels: {
                        formatter: function(val) {
                            return val.toFixed(0);
                        }
                    }
                },
                tooltip: {
                    y: {
                        formatter: function(val) {
                            return val.toFixed(2) + ' ر.س';
                        }
                    }
                }
            };
            
            var salesChart = new ApexCharts(document.getElementById('salesChart'), salesChartOptions);
            salesChart.render();
            
            // رسم بياني دائري لطرق الدفع
            var paymentMethodsOptions = {
                series: [65, 25, 10],
                chart: {
                    type: 'donut',
                    height: 300,
                    fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif'
                },
                labels: ['نقدي', 'بطاقة', 'آجل'],
                colors: ['#4361ee', '#3f37c9', '#f94144'],
                plotOptions: {
                    pie: {
                        donut: {
                            size: '50%',
                            labels: {
                                show: true,
                                name: {
                                    show: true,
                                    fontSize: '16px',
                                    fontWeight: 600
                                },
                                value: {
                                    show: true,
                                    fontSize: '14px',
                                    formatter: function(val) {
                                        return val + '%';
                                    }
                                },
                                total: {
                                    show: true,
                                    label: 'الإجمالي',
                                    formatter: function(w) {
                                        return '100%';
                                    }
                                }
                            }
                        }
                    }
                },
                dataLabels: {
                    enabled: false
                },
                legend: {
                    position: 'bottom',
                    horizontalAlign: 'center'
                },
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            height: 250
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }]
            };
            
            var paymentMethodsChart = new ApexCharts(document.getElementById('paymentMethodsChart'), paymentMethodsOptions);
            paymentMethodsChart.render();
            
            // مستمع حدث لمودال تفاصيل الفاتورة
            var invoiceModal = document.getElementById('invoiceModal');
            invoiceModal.addEventListener('show.bs.modal', function(event) {
                var button = event.relatedTarget;
                var invoiceId = button.getAttribute('data-invoice-id');
                var modalTitle = invoiceModal.querySelector('.modal-title');
                
                modalTitle.textContent = 'تفاصيل الفاتورة #' + invoiceId;
                
                // هنا يمكن إضافة كود AJAX لجلب تفاصيل الفاتورة من الخادم
            });
        });
    </script>
</body>
</html>