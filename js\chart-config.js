// ملف منفصل لتكوين الرسم البياني
var salesChartInstance = null;

// دالة لتهيئة الرسم البياني
function initSalesChart() {
    // تدمير الرسم البياني الحالي إذا كان موجودًا
    if (salesChartInstance) {
        salesChartInstance.destroy();
    }
    
    // الحصول على سياق الرسم البياني
    var ctx = document.getElementById('salesChart');
    if (!ctx) return;
    
    ctx = ctx.getContext('2d');
    
    // بيانات ثابتة للمبيعات
    var salesData = [3200, 2800, 4500, 3800, 5200, 4900, 6000];
    
    // إنشاء الرسم البياني
    salesChartInstance = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
            datasets: [{
                label: 'المبيعات',
                data: salesData,
                backgroundColor: 'rgba(67, 97, 238, 0.1)',
                borderColor: '#4361ee',
                borderWidth: 2,
                tension: 0.3,
                pointBackgroundColor: '#4361ee',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 5,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 1000 // تقليل مدة الرسوم المتحركة
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    return salesChartInstance;
}

// دالة لتحديث بيانات الرسم البياني
function updateChartData(period) {
    if (!salesChartInstance) return;
    
    var newData;
    
    if (period === 'day') {
        newData = [800, 1200, 950, 1400, 1100, 1300, 1500];
    } else if (period === 'week') {
        newData = [3200, 2800, 4500, 3800, 5200, 4900, 6000];
    } else if (period === 'month') {
        newData = [12000, 15000, 13500, 16000, 14500, 17000, 18500];
    }
    
    // تحديث بيانات الرسم البياني
    salesChartInstance.data.datasets[0].data = newData;
    salesChartInstance.update();
}

// إضافة مستمعي الأحداث عند تحميل المستند
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة الرسم البياني
    initSalesChart();
    
    // إضافة مستمعي الأحداث لأزرار تصفية الرسم البياني
    var dayButton = document.querySelector('.btn-group .btn:nth-child(1)');
    var weekButton = document.querySelector('.btn-group .btn:nth-child(2)');
    var monthButton = document.querySelector('.btn-group .btn:nth-child(3)');
    
    if (dayButton) {
        dayButton.addEventListener('click', function() {
            setActiveButton(this);
            updateChartData('day');
        });
    }
    
    if (weekButton) {
        weekButton.addEventListener('click', function() {
            setActiveButton(this);
            updateChartData('week');
        });
    }
    
    if (monthButton) {
        monthButton.addEventListener('click', function() {
            setActiveButton(this);
            updateChartData('month');
        });
    }
    
    // دالة لتعيين الزر النشط
    function setActiveButton(button) {
        document.querySelectorAll('.btn-group .btn').forEach(function(btn) {
            btn.classList.remove('active');
        });
        button.classList.add('active');
    }
    
    // مولد الباركود
    var generateBarcodeButton = document.getElementById('generateBarcode');
    if (generateBarcodeButton) {
        generateBarcodeButton.addEventListener('click', function() {
            var value = document.getElementById('barcodeValue').value;
            if (value) {
                JsBarcode("#barcode", value, {
                    format: "CODE128",
                    lineColor: "#4361ee",
                    width: 2,
                    height: 50,
                    displayValue: true
                });
            }
        });
    }
});