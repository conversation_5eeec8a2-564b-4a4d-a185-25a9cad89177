<?php
// Database connection parameters
$servername = "localhost";
$username = "root";  // Default XAMPP username
$password = "";      // Default XAMPP password

// Create connection
$conn = new mysqli($servername, $username, $password);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "Connected successfully to MySQL server<br>";

// Create database
$sql = "CREATE DATABASE IF NOT EXISTS sqlagentahmed";
if ($conn->query($sql) === TRUE) {
    echo "Database 'sqlagentahmed' created successfully or already exists<br>";
} else {
    echo "Error creating database: " . $conn->error . "<br>";
    exit;
}

// Select the database
$conn->select_db("sqlagentahmed");

// Read SQL file
$sql_file = file_get_contents('database_setup.sql');

// Remove comments and split into individual queries
$sql_file = preg_replace('/--.*$/m', '', $sql_file);
$queries = explode(';', $sql_file);

// Execute each query
$success = true;
foreach ($queries as $query) {
    $query = trim($query);
    if (empty($query)) continue;
    
    if ($conn->query($query) !== TRUE) {
        echo "Error executing query: " . $conn->error . "<br>";
        echo "Query: " . $query . "<br><br>";
        $success = false;
    }
}

if ($success) {
    echo "Database setup completed successfully!<br>";
} else {
    echo "Database setup completed with errors.<br>";
}

// Close connection
$conn->close();
?>