<?php
// بدء جلسة
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// استيراد ملف الاتصال بقاعدة البيانات
require_once 'config/db_connection.php';

// استعلام لجلب إحصائيات العملاء
$total_customers_query = "SELECT COUNT(*) as count FROM customers";
$total_customers_result = mysqli_query($conn, $total_customers_query);
$total_customers = mysqli_fetch_assoc($total_customers_result)['count'];

// إجمالي الأرصدة المحفوظة
$total_wallet_query = "SELECT SUM(wallet_balance) as total FROM customers WHERE wallet_balance > 0";
$total_wallet_result = mysqli_query($conn, $total_wallet_query);
$total_wallet = mysqli_fetch_assoc($total_wallet_result)['total'] ?? 0;

// إجمالي الديون
$total_debt_query = "SELECT SUM(debt_balance) as total FROM customers WHERE debt_balance > 0";
$total_debt_result = mysqli_query($conn, $total_debt_query);
$total_debt = mysqli_fetch_assoc($total_debt_result)['total'] ?? 0;

// عدد العملاء المدينين
$debt_customers_query = "SELECT COUNT(*) as count FROM customers WHERE debt_balance > 0";
$debt_customers_result = mysqli_query($conn, $debt_customers_query);
$debt_customers = mysqli_fetch_assoc($debt_customers_result)['count'];

// استعلام لجلب العملاء
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';
$search = isset($_GET['search']) ? $_GET['search'] : '';

$customers_query = "SELECT * FROM customers";

if ($filter == 'debt') {
    $customers_query .= " WHERE debt_balance > 0";
} elseif ($filter == 'wallet') {
    $customers_query .= " WHERE wallet_balance > 0";
}

if (!empty($search)) {
    if (strpos($customers_query, 'WHERE') !== false) {
        $customers_query .= " AND (name LIKE '%$search%' OR phone LIKE '%$search%' OR email LIKE '%$search%')";
    } else {
        $customers_query .= " WHERE name LIKE '%$search%' OR phone LIKE '%$search%' OR email LIKE '%$search%'";
    }
}

$customers_query .= " ORDER BY name ASC";
$customers_result = mysqli_query($conn, $customers_query);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - نظام إدارة المتجر</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- ApexCharts -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --whatsapp-color: #25D366;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --sidebar-width: 250px;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fb;
            color: #333;
            overflow-x: hidden;
        }
        
        /* تصميم الشريط الجانبي */
        .sidebar {
            height: 100vh;
            width: var(--sidebar-width);
            position: fixed;
            top: 0;
            right: 0;
            background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding-top: 20px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            z-index: 1000;
        }
        
        .sidebar .logo {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .logo h3 {
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 15px;
            border-radius: 8px;
            transition: all 0.3s;
            font-size: 16px;
        }
        
        .sidebar .nav-link:hover, 
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar .nav-link i {
            margin-left: 10px;
            width: 25px;
            text-align: center;
        }
        
        /* تصميم المحتوى الرئيسي */
        .main-content {
            margin-right: var(--sidebar-width);
            padding: 20px;
            transition: all 0.3s;
        }
        
        /* شريط التنقل العلوي */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        
        .search-bar {
            background-color: #f8f9fa;
            border-radius: 30px;
            padding: 8px 20px;
            border: 1px solid #eaeaea;
        }
        
        .search-bar input {
            background: transparent;
            border: none;
            width: 100%;
            padding: 5px 10px;
        }
        
        .search-bar input:focus {
            outline: none;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #f94144;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* بطاقات الإحصائيات */
        .stat-card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .stat-card .card-title {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-card .card-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .stat-card .card-footer {
            background: transparent;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 13px;
            padding: 8px 15px;
        }
        
        /* تصميم قسم العملاء */
        .customers-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .customer-card {
            border: 1px solid #eaeaea;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s;
        }
        
        .customer-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-color: var(--primary-color);
        }
        
        .customer-card .customer-info {
            display: flex;
            align-items: center;
        }
        
        .customer-card .customer-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: var(--primary-color);
            margin-left: 15px;
        }
        
        .customer-card .customer-details h5 {
            margin-bottom: 5px;
        }
        
        .customer-card .customer-contact {
            font-size: 14px;
            color: #6c757d;
        }
        
        .customer-card .customer-balance {
            margin-top: 15px;
            display: flex;
            gap: 15px;
        }
        
        .customer-card .balance-item {
            flex: 1;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }
        
        .customer-card .balance-item.wallet {
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
        }
        
        .customer-card .balance-item.debt {
            background-color: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
        }
        
        .customer-card .balance-item .balance-title {
            font-size: 12px;
            margin-bottom: 5px;
        }
        
        .customer-card .balance-item .balance-value {
            font-size: 16px;
            font-weight: 700;
        }
        
        .customer-card .customer-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
        }
        
        /* تصميم كشف الحساب */
        .account-statement {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
        }
        
        .statement-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .statement-summary {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .statement-summary-item {
            flex: 1;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .statement-summary-item.total {
            background-color: rgba(67, 97, 238, 0.1);
            color: var(--primary-color);
        }
        
        .statement-summary-item.paid {
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
        }
        
        .statement-summary-item.remaining {
            background-color: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
        }
        
        .statement-summary-item .summary-title {
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .statement-summary-item .summary-value {
            font-size: 20px;
            font-weight: 700;
        }
        
        .statement-table {
            margin-top: 20px;
        }
        
        .statement-table .transaction-type {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }
        
        .statement-table .transaction-type.invoice {
            background-color: var(--primary-color);
        }
        
        .statement-table .transaction-type.payment {
            background-color: var(--success-color);
        }
        
        .statement-table .transaction-type.debt {
            background-color: var(--danger-color);
        }
        
        .statement-table .transaction-type.wallet {
            background-color: var(--info-color);
        }
        
        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
                overflow: hidden;
            }
            
            .sidebar .logo h3, .sidebar .nav-link span {
                display: none;
            }
            
            .sidebar .nav-link {
                text-align: center;
                margin: 5px 10px;
            }
            
            .sidebar .nav-link i {
                margin-left: 0;
            }
            
            .main-content {
                margin-right: 70px;
            }
            
            .customer-card .customer-balance {
                flex-direction: column;
                gap: 10px;
            }
            
            .statement-summary {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-store"></i> متجرنا</h3>
        </div>
        <ul class="nav flex-column mt-4">
            <li class="nav-item">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales.php">
                    <i class="fas fa-shopping-cart"></i>
                    <span>المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales_management.php">
                    <i class="fas fa-chart-line"></i>
                    <span>إدارة المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="products.php">
                    <i class="fas fa-box"></i>
                    <span>المنتجات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="customers.php">
                    <i class="fas fa-users"></i>
                    <span>العملاء</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="suppliers.php">
                    <i class="fas fa-truck"></i>
                    <span>الموردين</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="purchases.php">
                    <i class="fas fa-shopping-basket"></i>
                    <span>المشتريات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="expenses.php">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>المصروفات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="financial.php">
                    <i class="fas fa-chart-pie"></i>
                    <span>الإدارة المالية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="barcode.php">
                    <i class="fas fa-barcode"></i>
                    <span>مولد الباركود</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="settings.php">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط التنقل العلوي -->
        <div class="top-navbar">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4 class="mb-0">إدارة العملاء</h4>
                    <p class="mb-0 text-muted">مرحباً بعودتك، <?php echo $_SESSION['username']; ?>!</p>
                </div>
                <div class="col-md-6 d-flex justify-content-end">
                    <div class="d-flex align-items-center">
                        <div class="search-bar me-3">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="بحث...">
                        </div>
                        <div class="position-relative me-3">
                            <button class="btn btn-light rounded-circle">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-light rounded-circle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.php">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="row">
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-primary-subtle text-primary me-3">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <div class="card-title">إجمالي العملاء</div>
                            <div class="card-value"><?php echo $total_customers; ?></div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-user-plus"></i> إضافة عميل جديد
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-success-subtle text-success me-3">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div>
                            <div class="card-title">الأرصدة المحفوظة</div>
                            <div class="card-value"><?php echo number_format($total_wallet, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <a href="?filter=wallet" class="text-decoration-none">عرض العملاء ذوي الأرصدة</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-danger-subtle text-danger me-3">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <div>
                            <div class="card-title">إجمالي الديون</div>
                            <div class="card-value"><?php echo number_format($total_debt, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <a href="?filter=debt" class="text-decoration-none">عرض العملاء المدينين</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-warning-subtle text-warning me-3">
                            <i class="fas fa-user-tag"></i>
                        </div>
                        <div>
                            <div class="card-title">العملاء المدينين</div>
                            <div class="card-value"><?php echo $debt_customers; ?></div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-percentage"></i> <?php echo $total_customers > 0 ? round(($debt_customers / $total_customers) * 100) : 0; ?>% من العملاء
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم البحث والفلترة -->
        <div class="customers-container mb-4">
            <div class="row mb-4">
                <div class="col-md-8">
                    <form action="" method="get" class="d-flex gap-2">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="بحث عن عميل..." name="search" value="<?php echo $search; ?>">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <select class="form-select" name="filter" onchange="this.form.submit()">
                            <option value="" <?php echo $filter == '' ? 'selected' : ''; ?>>جميع العملاء</option>
                            <option value="debt" <?php echo $filter == 'debt' ? 'selected' : ''; ?>>العملاء المدينين</option>
                            <option value="wallet" <?php echo $filter == 'wallet' ? 'selected' : ''; ?>>العملاء ذوي الأرصدة</option>
                        </select>
                    </form>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                        <i class="fas fa-user-plus me-2"></i> إضافة عميل جديد
                    </button>
                </div>
            </div>

            <!-- قائمة العملاء -->
            <div class="row">
                <?php
                // عرض العملاء
                if (mysqli_num_rows($customers_result) > 0) {
                    while ($customer = mysqli_fetch_assoc($customers_result)) {
                        $wallet_balance = $customer['wallet_balance'] ?? 0;
                        $debt_balance = $customer['debt_balance'] ?? 0;
                        
                        echo '<div class="col-md-6 col-lg-4">';
                        echo '<div class="customer-card">';
                        echo '<div class="customer-info">';
                        echo '<div class="customer-avatar">';
                        echo '<i class="fas fa-user"></i>';
                        echo '</div>';
                        echo '<div class="customer-details">';
                        echo '<h5>' . $customer['name'] . '</h5>';
                        echo '<div class="customer-contact">';
                        echo '<div><i class="fas fa-phone me-2"></i>' . $customer['phone'] . '</div>';
                        if (!empty($customer['email'])) {
                            echo '<div><i class="fas fa-envelope me-2"></i>' . $customer['email'] . '</div>';
                        }
                        echo '</div>';
                        echo '</div>';
                        echo '</div>';
                        
                        echo '<div class="customer-balance">';
                        echo '<div class="balance-item wallet">';
                        echo '<div class="balance-title">الرصيد المحفوظ</div>';
                        echo '<div class="balance-value">' . number_format($wallet_balance, 2) . ' ر.س</div>';
                        echo '</div>';
                        echo '<div class="balance-item debt">';
                        echo '<div class="balance-title">الرصيد المدين</div>';
                        echo '<div class="balance-value">' . number_format($debt_balance, 2) . ' ر.س</div>';
                        echo '</div>';
                        echo '</div>';
                        
                        echo '<div class="customer-actions">';
                        echo '<button class="btn btn-sm btn-primary flex-grow-1" onclick="viewStatement(' . $customer['id'] . ')">';
                        echo '<i class="fas fa-file-invoice me-1"></i> كشف حساب';
                        echo '</button>';
                        echo '<button class="btn btn-sm btn-success flex-grow-1" data-bs-toggle="modal" data-bs-target="#addPaymentModal" data-customer-id="' . $customer['id'] . '" data-customer-name="' . $customer['name'] . '">';
                        echo '<i class="fas fa-money-bill-wave me-1"></i> دفعة جديدة';
                        echo '</button>';
                        echo '<button class="btn btn-sm btn-info flex-grow-1" data-bs-toggle="modal" data-bs-target="#editCustomerModal" data-customer-id="' . $customer['id'] . '">';
                        echo '<i class="fas fa-edit me-1"></i> تعديل';
                        echo '</button>';
                        echo '</div>';
                        echo '</div>';
                        echo '</div>';
                    }
                } else {
                    echo '<div class="col-12">';
                    echo '<div class="alert alert-info">لا يوجد عملاء مطابقين للبحث</div>';
                    echo '</div>';
                }
                ?>
            </div>
        </div>
    </div>
    
    <!-- مودال إضافة عميل جديد -->
    <div class="modal fade" id="addCustomerModal" tabindex="-1" aria-labelledby="addCustomerModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCustomerModalLabel">إضافة عميل جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addCustomerForm">
                        <div class="mb-3">
                            <label for="customerName" class="form-label">اسم العميل</label>
                            <input type="text" class="form-control" id="customerName" required>
                        </div>
                        <div class="mb-3">
                            <label for="customerPhone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="customerPhone" required>
                        </div>
                        <div class="mb-3">
                            <label for="customerEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="customerEmail">
                        </div>
                        <div class="mb-3">
                            <label for="customerAddress" class="form-label">العنوان</label>
                            <textarea class="form-control" id="customerAddress" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="customerNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="customerNotes" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveCustomer">حفظ</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال تعديل بيانات العميل -->
    <div class="modal fade" id="editCustomerModal" tabindex="-1" aria-labelledby="editCustomerModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editCustomerModalLabel">تعديل بيانات العميل</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editCustomerForm">
                        <input type="hidden" id="editCustomerId">
                        <div class="mb-3">
                            <label for="editCustomerName" class="form-label">اسم العميل</label>
                            <input type="text" class="form-control" id="editCustomerName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editCustomerPhone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="editCustomerPhone" required>
                        </div>
                        <div class="mb-3">
                            <label for="editCustomerEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="editCustomerEmail">
                        </div>
                        <div class="mb-3">
                            <label for="editCustomerAddress" class="form-label">العنوان</label>
                            <textarea class="form-control" id="editCustomerAddress" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editCustomerNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="editCustomerNotes" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="updateCustomer">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال إضافة دفعة جديدة -->
    <div class="modal fade" id="addPaymentModal" tabindex="-1" aria-labelledby="addPaymentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addPaymentModalLabel">إضافة دفعة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addPaymentForm">
                        <input type="hidden" id="paymentCustomerId">
                        <div class="mb-3">
                            <label class="form-label">اسم العميل</label>
                            <input type="text" class="form-control" id="paymentCustomerName" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الرصيد المدين الحالي</label>
                            <input type="text" class="form-control" id="currentDebtBalance" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="paymentAmount" class="form-label">مبلغ الدفعة</label>
                            <input type="number" class="form-control" id="paymentAmount" min="0" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label for="paymentMethod" class="form-label">طريقة الدفع</label>
                            <select class="form-select" id="paymentMethod" required>
                                <option value="cash">نقدي</option>
                                <option value="card">بطاقة</option>
                                <option value="bank">تحويل بنكي</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="paymentNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="paymentNotes" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="savePayment">حفظ الدفعة</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال كشف حساب العميل -->
    <div class="modal fade" id="statementModal" tabindex="-1" aria-labelledby="statementModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="statementModalLabel">كشف حساب العميل</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="account-statement">
                        <div class="statement-header">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 id="statementCustomerName">أحمد محمد</h5>
                                    <p class="mb-1" id="statementCustomerPhone">**********</p>
                                    <p id="statementCustomerEmail"><EMAIL></p>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <p class="mb-1">الفترة: <span id="statementPeriod">01/01/2023 - 31/12/2023</span></p>
                                    <p>تاريخ الإصدار: <span id="statementDate"><?php echo date('Y-m-d'); ?></span></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="statement-summary">
                            <div class="statement-summary-item total">
                                <div class="summary-title">إجمالي المشتريات</div>
                                <div class="summary-value" id="statementTotalPurchases">5,000.00 ر.س</div>
                            </div>
                            <div class="statement-summary-item paid">
                                <div class="summary-title">إجمالي المدفوعات</div>
                                <div class="summary-value" id="statementTotalPayments">4,500.00 ر.س</div>
                            </div>
                            <div class="statement-summary-item remaining">
                                <div class="summary-title">الرصيد المتبقي</div>
                                <div class="summary-value" id="statementRemainingBalance">500.00 ر.س</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">الرصيد المحفوظ</h6>
                                        <p class="card-text text-success fw-bold" id="statementWalletBalance">100.00 ر.س</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">الرصيد المدين</h6>
                                        <p class="card-text text-danger fw-bold" id="statementDebtBalance">500.00 ر.س</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="statement-table">
                            <h6 class="mb-3">سجل المعاملات</h6>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>النوع</th>
                                            <th>التفاصيل</th>
                                            <th>المبلغ</th>
                                            <th>الرصيد</th>
                                        </tr>
                                    </thead>
                                    <tbody id="statementTransactions">
                                        <!-- سيتم إضافة المعاملات هنا بواسطة JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary">
                        <i class="fas fa-print me-2"></i> طباعة
                    </button>
                    <button type="button" class="btn btn-success">
                        <i class="fas fa-file-pdf me-2"></i> تصدير PDF
                    </button>
                    <button type="button" class="btn btn-whatsapp">
                        <i class="fab fa-whatsapp me-2"></i> إرسال عبر واتساب
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i> إغلاق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // مستمع حدث لمودال تعديل العميل
            var editCustomerModal = document.getElementById('editCustomerModal');
            if (editCustomerModal) {
                editCustomerModal.addEventListener('show.bs.modal', function(event) {
                    var button = event.relatedTarget;
                    var customerId = button.getAttribute('data-customer-id');
                    
                    // هنا يمكن إضافة كود AJAX لجلب بيانات العميل من الخادم
                    // وملء النموذج بالبيانات
                    
                    document.getElementById('editCustomerId').value = customerId;
                });
            }
            
            // مستمع حدث لمودال إضافة دفعة
            var addPaymentModal = document.getElementById('addPaymentModal');
            if (addPaymentModal) {
                addPaymentModal.addEventListener('show.bs.modal', function(event) {
                    var button = event.relatedTarget;
                    var customerId = button.getAttribute('data-customer-id');
                    var customerName = button.getAttribute('data-customer-name');
                    
                    document.getElementById('paymentCustomerId').value = customerId;
                    document.getElementById('paymentCustomerName').value = customerName;
                    
                    // هنا يمكن إضافة كود AJAX لجلب الرصيد المدين الحالي للعميل
                    // وعرضه في الحقل المخصص
                });
            }
            
            // دالة لعرض كشف حساب العميل
            window.viewStatement = function(customerId) {
                // هنا يمكن إضافة كود AJAX لجلب بيانات كشف حساب العميل من الخادم
                
                // عرض المودال
                var statementModal = new bootstrap.Modal(document.getElementById('statementModal'));
                statementModal.show();
                
                // ملء بيانات كشف الحساب (هذه بيانات تجريبية)
                document.getElementById('statementCustomerName').textContent = 'أحمد محمد';
                document.getElementById('statementCustomerPhone').textContent = '**********';
                document.getElementById('statementCustomerEmail').textContent = '<EMAIL>';
                document.getElementById('statementPeriod').textContent = '01/01/2023 - 31/12/2023';
                document.getElementById('statementTotalPurchases').textContent = '5,000.00 ر.س';
                document.getElementById('statementTotalPayments').textContent = '4,500.00 ر.س';
                document.getElementById('statementRemainingBalance').textContent = '500.00 ر.س';
                document.getElementById('statementWalletBalance').textContent = '100.00 ر.س';
                document.getElementById('statementDebtBalance').textContent = '500.00 ر.س';
                
                // إضافة المعاملات إلى الجدول
                var transactions = [
                    {
                        date: '2023-12-15',
                        type: 'invoice',
                        details: 'فاتورة مبيعات #12345',
                        amount: '1,500.00',
                        balance: '1,500.00',
                        typeText: 'فاتورة'
                    },
                    {
                        date: '2023-12-16',
                        type: 'payment',
                        details: 'دفعة نقدية',
                        amount: '1,000.00',
                        balance: '500.00',
                        typeText: 'دفعة'
                    },
                    {
                        date: '2023-12-20',
                        type: 'invoice',
                        details: 'فاتورة مبيعات #12346',
                        amount: '800.00',
                        balance: '1,300.00',
                        typeText: 'فاتورة'
                    },
                    {
                        date: '2023-12-25',
                        type: 'payment',
                        details: 'دفعة نقدية',
                        amount: '800.00',
                        balance: '500.00',
                        typeText: 'دفعة'
                    },
                    {
                        date: '2023-12-28',
                        type: 'wallet',
                        details: 'إضافة رصيد محفوظ',
                        amount: '100.00',
                        balance: '500.00',
                        typeText: 'رصيد'
                    }
                ];
                
                var transactionsHTML = '';
                transactions.forEach(function(transaction) {
                    transactionsHTML += `
                        <tr>
                            <td>${transaction.date}</td>
                            <td>
                                <div class="transaction-type ${transaction.type}">
                                    <i class="${transaction.type === 'invoice' ? 'fas fa-file-invoice' : transaction.type === 'payment' ? 'fas fa-money-bill-wave' : 'fas fa-wallet'}"></i>
                                </div>
                            </td>
                            <td>${transaction.details}</td>
                            <td class="${transaction.type === 'invoice' ? 'text-danger' : 'text-success'}">${transaction.type === 'invoice' ? '-' : '+'} ${transaction.amount} ر.س</td>
                            <td>${transaction.balance} ر.س</td>
                        </tr>
                    `;
                });
                
                document.getElementById('statementTransactions').innerHTML = transactionsHTML;
            };
            
            // مستمع حدث لحفظ العميل الجديد
            document.getElementById('saveCustomer').addEventListener('click', function() {
                var form = document.getElementById('addCustomerForm');
                
                // التحقق من صحة النموذج
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }
                
                // جمع بيانات النموذج
                var customerData = {
                    name: document.getElementById('customerName').value,
                    phone: document.getElementById('customerPhone').value,
                    email: document.getElementById('customerEmail').value,
                    address: document.getElementById('customerAddress').value,
                    notes: document.getElementById('customerNotes').value
                };
                
                // هنا يمكن إضافة كود AJAX لإرسال البيانات إلى الخادم
                
                // إغلاق المودال
                var modal = bootstrap.Modal.getInstance(document.getElementById('addCustomerModal'));
                modal.hide();
                
                // إعادة تحميل الصفحة أو تحديث قائمة العملاء
                alert('تم إضافة العميل بنجاح');
                // window.location.reload();
            });
            
            // مستمع حدث لتحديث بيانات العميل
            document.getElementById('updateCustomer').addEventListener('click', function() {
                var form = document.getElementById('editCustomerForm');
                
                // التحقق من صحة النموذج
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }
                
                // جمع بيانات النموذج
                var customerData = {
                    id: document.getElementById('editCustomerId').value,
                    name: document.getElementById('editCustomerName').value,
                    phone: document.getElementById('editCustomerPhone').value,
                    email: document.getElementById('editCustomerEmail').value,
                    address: document.getElementById('editCustomerAddress').value,
                    notes: document.getElementById('editCustomerNotes').value
                };
                
                // هنا يمكن إضافة كود AJAX لإرسال البيانات إلى الخادم
                
                // إغلاق المودال
                var modal = bootstrap.Modal.getInstance(document.getElementById('editCustomerModal'));
                modal.hide();
                
                // إعادة تحميل الصفحة أو تحديث بيانات العميل
                alert('تم تحديث بيانات العميل بنجاح');
                // window.location.reload();
            });
            
            // مستمع حدث لحفظ الدفعة الجديدة
            document.getElementById('savePayment').addEventListener('click', function() {
                var form = document.getElementById('addPaymentForm');
                
                // التحقق من صحة النموذج
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }
                
                // جمع بيانات النموذج
                var paymentData = {
                    customerId: document.getElementById('paymentCustomerId').value,
                    amount: document.getElementById('paymentAmount').value,
                    method: document.getElementById('paymentMethod').value,
                    notes: document.getElementById('paymentNotes').value
                };
                
                // هنا يمكن إضافة كود AJAX لإرسال البيانات إلى الخادم
                
                // إغلاق المودال
                var modal = bootstrap.Modal.getInstance(document.getElementById('addPaymentModal'));
                modal.hide();
                
                // إعادة تحميل الصفحة أو تحديث بيانات العميل
                alert('تم إضافة الدفعة بنجاح');
                // window.location.reload();
            });
        });
    </script>
</body>
</html>