<?php
// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page
    header('Location: index.html');
    exit;
}

// Get user information
$userId = $_SESSION['user_id'];
$username = $_SESSION['username'];
$fullName = $_SESSION['full_name'] ?? 'أحمد';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المتجر</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- ApexCharts -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --success-color: #4cc9f0;
            --warning-color: #ff9e00;
            --danger-color: #f94144;
            --info-color: #4895ef;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --sidebar-width: 250px;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fb;
            color: #333;
            overflow-x: hidden;
        }
        
        /* تصميم الشريط الجانبي */
        .sidebar {
            height: 100vh;
            width: var(--sidebar-width);
            position: fixed;
            top: 0;
            right: 0;
            background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding-top: 20px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            z-index: 1000;
        }
        
        .sidebar .logo {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .logo h3 {
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 15px;
            border-radius: 8px;
            transition: all 0.3s;
            font-size: 16px;
        }
        
        .sidebar .nav-link:hover, 
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar .nav-link i {
            margin-left: 10px;
            width: 25px;
            text-align: center;
        }
        
        /* تصميم المحتوى الرئيسي */
        .main-content {
            margin-right: var(--sidebar-width);
            padding: 20px;
            transition: all 0.3s;
        }
        
        /* شريط التنقل العلوي */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        
        .search-bar {
            background-color: #f8f9fa;
            border-radius: 30px;
            padding: 8px 20px;
            border: 1px solid #eaeaea;
        }
        
        .search-bar input {
            background: transparent;
            border: none;
            width: 100%;
            padding: 5px 10px;
        }
        
        .search-bar input:focus {
            outline: none;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #f94144;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* بطاقات الإحصائيات */
        .stat-card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .stat-card .card-title {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-card .card-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .stat-card .card-footer {
            background: transparent;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 13px;
            padding: 8px 15px;
        }
        
        /* الرسوم البيانية */
        .chart-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        /* جدول أحدث الطلبات */
        .recent-orders {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .recent-orders table {
            width: 100%;
        }
        
        .recent-orders .badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 500;
        }
        
        .recent-orders .status-completed {
            background-color: #e6f4ea;
            color: #0f9d58;
        }
        
        .recent-orders .status-pending {
            background-color: #fef7e0;
            color: #f9ab00;
        }
        
        .recent-orders .status-canceled {
            background-color: #fce8e6;
            color: #ea4335;
        }
        
        /* منتجات قاربت على النفاذ */
        .low-stock {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
        }
        
        .low-stock-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .low-stock-item:last-child {
            border-bottom: none;
        }
        
        .low-stock-item img {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            object-fit: cover;
            margin-left: 15px;
        }
        
        .progress {
            height: 8px;
            border-radius: 4px;
        }
        
        /* مولد الباركود */
        .barcode-generator {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .barcode-container {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #eee;
            text-align: center;
        }
        
        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
                overflow: hidden;
            }
            
            .sidebar .logo h3, .sidebar .nav-link span {
                display: none;
            }
            
            .sidebar .nav-link {
                text-align: center;
                margin: 5px 10px;
            }
            
            .sidebar .nav-link i {
                margin-left: 0;
            }
            
            .main-content {
                margin-right: 70px;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-store"></i> متجرنا</h3>
        </div>
        <ul class="nav flex-column mt-4">
            <li class="nav-item">
                <a class="nav-link active" href="dashboard.php">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales.php">
                    <i class="fas fa-shopping-cart"></i>
                    <span>المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales_management.php">
                    <i class="fas fa-chart-line"></i>
                    <span>إدارة المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="products.php">
                    <i class="fas fa-box"></i>
                    <span>المنتجات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customers.php">
                    <i class="fas fa-users"></i>
                    <span>العملاء</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="suppliers.php">
                    <i class="fas fa-truck"></i>
                    <span>الموردين</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="purchases.php">
                    <i class="fas fa-shopping-basket"></i>
                    <span>المشتريات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="expenses.php">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>المصروفات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="cashbox.php">
                    <i class="fas fa-cash-register"></i>
                    <span>الخزنة</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="financial.php">
                    <i class="fas fa-chart-pie"></i>
                    <span>الإدارة المالية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="barcode.php">
                    <i class="fas fa-barcode"></i>
                    <span>مولد الباركود</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="settings.php">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط التنقل العلوي -->
        <div class="top-navbar">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4 class="mb-0">الرئيسية</h4>
                    <p class="mb-0 text-muted">مرحباً بعودتك، <?php echo htmlspecialchars($fullName); ?>!</p>
                </div>
                <div class="col-md-6 d-flex justify-content-end">
                    <div class="d-flex align-items-center">
                        <div class="search-bar me-3">
                            <i class="fas fa-search text-muted"></i>
                            <input type="text" placeholder="بحث...">
                        </div>
                        <div class="position-relative me-3">
                            <button class="btn btn-light rounded-circle p-2">
                                <i class="fas fa-bell"></i>
                            </button>
                            <span class="notification-badge">5</span>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-light rounded-circle p-2 dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="row">
            <div class="col-md-3">
                <a href="sales_management.php" class="text-decoration-none">
                    <div class="stat-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <p class="card-title">إجمالي المبيعات</p>
                                    <h3 class="card-value">24,750 ر.س</h3>
                                </div>
                                <div class="card-icon bg-primary text-white">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <span class="text-success"><i class="fas fa-arrow-up"></i> 12.5%</span> مقارنة بالشهر الماضي
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-md-3">
                <a href="sales_management.php" class="text-decoration-none">
                    <div class="stat-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <p class="card-title">عدد الطلبات</p>
                                    <h3 class="card-value">156</h3>
                                </div>
                                <div class="card-icon bg-success text-white">
                                    <i class="fas fa-clipboard-list"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <span class="text-success"><i class="fas fa-arrow-up"></i> 8.3%</span> مقارنة بالشهر الماضي
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-md-3">
                <a href="customers.php" class="text-decoration-none">
                    <div class="stat-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <p class="card-title">عدد العملاء</p>
                                    <h3 class="card-value">85</h3>
                                </div>
                                <div class="card-icon bg-warning text-white">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <span class="text-success"><i class="fas fa-arrow-up"></i> 5.2%</span> مقارنة بالشهر الماضي
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-md-3">
                <a href="cashbox.php" class="text-decoration-none">
                    <div class="stat-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <p class="card-title">صافي الربح</p>
                                    <h3 class="card-value">8,320 ر.س</h3>
                                </div>
                                <div class="card-icon bg-danger text-white">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <span class="text-danger"><i class="fas fa-arrow-down"></i> 3.1%</span> مقارنة بالشهر الماضي
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- الرسوم البيانية -->
        <div class="row">
            <div class="col-md-8">
                <div class="chart-container">
                    <div class="chart-header">
                        <h5 class="mb-0">تحليل المبيعات</h5>
                        <div class="btn-group">
                            <a href="sales_management.php?period=day" class="btn btn-sm btn-outline-secondary">اليوم</a>
                            <a href="sales_management.php?period=week" class="btn btn-sm btn-outline-secondary active">الأسبوع</a>
                            <a href="sales_management.php?period=month" class="btn btn-sm btn-outline-secondary">الشهر</a>
                        </div>
                    </div>
                    <div id="salesChart" style="height: 300px;"></div>
                </div>
                
                <!-- جدول أحدث الطلبات -->
                <div class="recent-orders">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="mb-0">أحدث الطلبات</h5>
                        <a href="sales_management.php" class="btn btn-sm btn-primary">عرض الكل</a>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>العميل</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>#ORD-7895</td>
                                    <td>محمد أحمد</td>
                                    <td>15 يونيو 2023</td>
                                    <td>750 ر.س</td>
                                    <td><span class="badge status-completed">مكتمل</span></td>
                                </tr>
                                <tr>
                                    <td>#ORD-7894</td>
                                    <td>سارة محمد</td>
                                    <td>15 يونيو 2023</td>
                                    <td>1,250 ر.س</td>
                                    <td><span class="badge status-pending">قيد التنفيذ</span></td>
                                </tr>
                                <tr>
                                    <td>#ORD-7893</td>
                                    <td>أحمد علي</td>
                                    <td>14 يونيو 2023</td>
                                    <td>450 ر.س</td>
                                    <td><span class="badge status-completed">مكتمل</span></td>
                                </tr>
                                <tr>
                                    <td>#ORD-7892</td>
                                    <td>فاطمة خالد</td>
                                    <td>14 يونيو 2023</td>
                                    <td>920 ر.س</td>
                                    <td><span class="badge status-canceled">ملغي</span></td>
                                </tr>
                                <tr>
                                    <td>#ORD-7891</td>
                                    <td>عمر محمود</td>
                                    <td>13 يونيو 2023</td>
                                    <td>1,500 ر.س</td>
                                    <td><span class="badge status-completed">مكتمل</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <!-- رسم بياني دائري لتوزيع المصروفات -->
                <div class="chart-container mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">توزيع المصروفات</h5>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="expensesDropdown" data-bs-toggle="dropdown">
                                الشهر الحالي
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="expensesDropdown">
                                <li><a class="dropdown-item" href="expenses.php?period=month">الشهر الحالي</a></li>
                                <li><a class="dropdown-item" href="expenses.php?period=quarter">الربع الحالي</a></li>
                                <li><a class="dropdown-item" href="#">السنة الحالية</a></li>
                            </ul>
                        </div>
                    </div>
                    <div id="expensesChart" style="height: 300px;"></div>
                </div>
                
                <!-- منتجات قاربت على النفاذ -->
                <div class="low-stock">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="mb-0">منتجات قاربت على النفاذ</h5>
                        <a href="products.php" class="btn btn-sm btn-primary">عرض الكل</a>
                    </div>
                    <div class="low-stock-item">
                        <img src="https://via.placeholder.com/50" alt="منتج">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">هاتف ذكي سامسونج جالاكسي</h6>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="progress w-75">
                                    <div class="progress-bar bg-danger" role="progressbar" style="width: 15%"></div>
                                </div>
                                <span class="text-danger">3 متبقي</span>
                            </div>
                        </div>
                    </div>
                    <div class="low-stock-item">
                        <img src="https://via.placeholder.com/50" alt="منتج">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">سماعات بلوتوث لاسلكية</h6>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="progress w-75">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 25%"></div>
                                </div>
                                <span class="text-warning">5 متبقي</span>
                            </div>
                        </div>
                    </div>
                    <div class="low-stock-item">
                        <img src="https://via.placeholder.com/50" alt="منتج">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">شاحن سريع USB-C</h6>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="progress w-75">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 30%"></div>
                                </div>
                                <span class="text-warning">6 متبقي</span>
                            </div>
                        </div>
                    </div>
                    <div class="low-stock-item">
                        <img src="https://via.placeholder.com/50" alt="منتج">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">حافظة هاتف مقاومة للصدمات</h6>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="progress w-75">
                                    <div class="progress-bar bg-danger" role="progressbar" style="width: 10%"></div>
                                </div>
                                <span class="text-danger">2 متبقي</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // كود الرسم البياني باستخدام ApexCharts
        document.addEventListener('DOMContentLoaded', function() {
            // بيانات ثابتة للمبيعات
            var weekData = [3200, 2800, 4500, 3800, 5200, 4900, 6000];
            var dayData = [800, 1200, 950, 1400, 1100, 1300, 1500];
            var monthData = [12000, 15000, 13500, 16000, 14500, 17000, 18500];
            var categories = ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'];
            
            // خيارات الرسم البياني للمبيعات
            var salesOptions = {
                series: [{
                    name: 'المبيعات',
                    data: weekData
                }],
                chart: {
                    height: 300,
                    type: 'area',
                    fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
                    toolbar: {
                        show: false
                    },
                    animations: {
                        enabled: false
                    }
                },
                colors: ['#4361ee'],
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth',
                    width: 2
                },
                fill: {
                    type: 'gradient',
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.7,
                        opacityTo: 0.2,
                        stops: [0, 90, 100]
                    }
                },
                xaxis: {
                    categories: categories
                },
                yaxis: {
                    labels: {
                        formatter: function(val) {
                            return val.toFixed(0);
                        }
                    }
                },
                tooltip: {
                    y: {
                        formatter: function(val) {
                            return val.toFixed(0) + ' ر.س';
                        }
                    }
                }
            };
            
            // إنشاء الرسم البياني للمبيعات
            var salesChartElement = document.getElementById('salesChart');
            if (salesChartElement) {
                var salesChart = new ApexCharts(salesChartElement, salesOptions);
                salesChart.render();
                
                // أزرار تصفية الرسم البياني
                var dayBtn = document.querySelector('.btn-group .btn:nth-child(1)');
                var weekBtn = document.querySelector('.btn-group .btn:nth-child(2)');
                var monthBtn = document.querySelector('.btn-group .btn:nth-child(3)');
                
                if (dayBtn) {
                    dayBtn.addEventListener('click', function() {
                        updateSalesChart(this, dayData);
                    });
                }
                
                if (weekBtn) {
                    weekBtn.addEventListener('click', function() {
                        updateSalesChart(this, weekData);
                    });
                }
                
                if (monthBtn) {
                    monthBtn.addEventListener('click', function() {
                        updateSalesChart(this, monthData);
                    });
                }
                
                function updateSalesChart(button, data) {
                    // تحديث الزر النشط
                    document.querySelectorAll('.btn-group .btn').forEach(function(btn) {
                        btn.classList.remove('active');
                    });
                    button.classList.add('active');
                    
                    // تحديث بيانات الرسم البياني
                    salesChart.updateSeries([{
                        data: data
                    }]);
                }
            }
            
            // بيانات الرسم البياني الدائري للمصروفات
            var expensesData = [
                { category: 'الإيجار', amount: 2500 },
                { category: 'الرواتب', amount: 5000 },
                { category: 'المرافق', amount: 1200 },
                { category: 'المخزون', amount: 3800 },
                { category: 'التسويق', amount: 1500 },
                { category: 'أخرى', amount: 800 }
            ];
            
            // خيارات الرسم البياني الدائري للمصروفات
            var expensesOptions = {
                series: expensesData.map(item => item.amount),
                chart: {
                    type: 'donut',
                    height: 300,
                    fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
                    animations: {
                        enabled: false
                    }
                },
                labels: expensesData.map(item => item.category),
                colors: ['#4361ee', '#3f37c9', '#4cc9f0', '#ff9e00', '#f94144', '#4895ef'],
                plotOptions: {
                    pie: {
                        donut: {
                            size: '50%',
                            labels: {
                                show: true,
                                name: {
                                    show: true,
                                    fontSize: '16px',
                                    fontWeight: 600
                                },
                                value: {
                                    show: true,
                                    fontSize: '14px',
                                    formatter: function(val) {
                                        return val + ' ر.س';
                                    }
                                },
                                total: {
                                    show: true,
                                    label: 'الإجمالي',
                                    formatter: function(w) {
                                        return w.globals.seriesTotals.reduce((a, b) => a + b, 0) + ' ر.س';
                                    }
                                }
                            }
                        }
                    }
                },
                dataLabels: {
                    enabled: false
                },
                legend: {
                    position: 'bottom',
                    horizontalAlign: 'center'
                },
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            height: 250
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }]
            };
            
            // إنشاء الرسم البياني الدائري للمصروفات
            var expensesChartElement = document.getElementById('expensesChart');
            if (expensesChartElement) {
                var expensesChart = new ApexCharts(expensesChartElement, expensesOptions);
                expensesChart.render();
            }
            
            // تم إزالة كود مولد الباركود من الصفحة الرئيسية
        });
    </script>
</body>
</html>