<?php
// بدء جلسة
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// استيراد ملف الاتصال بقاعدة البيانات
require_once 'config/db_connection.php';

// التحقق من وجود جدول المشتريات
$table_exists_query = "SHOW TABLES LIKE 'purchases'";
$table_exists_result = mysqli_query($conn, $table_exists_query);
$table_exists = mysqli_num_rows($table_exists_result) > 0;

// تهيئة المتغيرات الإحصائية
$total_purchases_count = 0;
$total_purchases_amount = 0;
$unpaid_purchases_count = 0;
$unpaid_purchases_amount = 0;
$recent_purchases_count = 0;
$recent_purchases_amount = 0;

// استعلام لجلب إحصائيات المشتريات إذا كان الجدول موجودًا
if ($table_exists) {
    // إجمالي المشتريات
    $total_purchases_query = "SELECT COUNT(*) as count, SUM(total_amount) as total FROM purchases";
    $total_purchases_result = mysqli_query($conn, $total_purchases_query);
    if ($total_purchases_result && mysqli_num_rows($total_purchases_result) > 0) {
        $total_purchases_data = mysqli_fetch_assoc($total_purchases_result);
        $total_purchases_count = $total_purchases_data['count'] ?? 0;
        $total_purchases_amount = $total_purchases_data['total'] ?? 0;
    }
    
    // المشتريات غير المدفوعة
    $unpaid_purchases_query = "SELECT COUNT(*) as count, SUM(total_amount - paid_amount) as total FROM purchases WHERE payment_status != 'paid'";
    $unpaid_purchases_result = mysqli_query($conn, $unpaid_purchases_query);
    if ($unpaid_purchases_result && mysqli_num_rows($unpaid_purchases_result) > 0) {
        $unpaid_purchases_data = mysqli_fetch_assoc($unpaid_purchases_result);
        $unpaid_purchases_count = $unpaid_purchases_data['count'] ?? 0;
        $unpaid_purchases_amount = $unpaid_purchases_data['total'] ?? 0;
    }
    
    // المشتريات الحديثة
    $recent_purchases_query = "SELECT COUNT(*) as count, SUM(total_amount) as total FROM purchases WHERE purchase_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
    $recent_purchases_result = mysqli_query($conn, $recent_purchases_query);
    if ($recent_purchases_result && mysqli_num_rows($recent_purchases_result) > 0) {
        $recent_purchases_data = mysqli_fetch_assoc($recent_purchases_result);
        $recent_purchases_count = $recent_purchases_data['count'] ?? 0;
        $recent_purchases_amount = $recent_purchases_data['total'] ?? 0;
    }
}

// تهيئة مصفوفة الموردين
$suppliers = [];

// التحقق من وجود جدول الموردين
$suppliers_table_exists_query = "SHOW TABLES LIKE 'suppliers'";
$suppliers_table_exists_result = mysqli_query($conn, $suppliers_table_exists_query);
$suppliers_table_exists = mysqli_num_rows($suppliers_table_exists_result) > 0;

// استعلام لجلب الموردين إذا كان الجدول موجودًا
if ($suppliers_table_exists) {
    $suppliers_query = "SELECT id, name FROM suppliers ORDER BY name";
    $suppliers_result = mysqli_query($conn, $suppliers_query);
    if ($suppliers_result) {
        while ($supplier = mysqli_fetch_assoc($suppliers_result)) {
            $suppliers[] = $supplier;
        }
    }
}

// معالجة البحث والفلترة
$search = isset($_GET['search']) ? $_GET['search'] : '';
$supplier_filter = isset($_GET['supplier']) ? $_GET['supplier'] : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';

// تهيئة متغير نتيجة المشتريات
$purchases_result = false;

// بناء استعلام المشتريات إذا كانت الجداول موجودة
if ($table_exists && $suppliers_table_exists) {
    $purchases_query = "SELECT p.*, s.name as supplier_name 
                       FROM purchases p 
                       LEFT JOIN suppliers s ON p.supplier_id = s.id 
                       WHERE 1=1";
    
    if (!empty($search)) {
        $purchases_query .= " AND (p.invoice_number LIKE '%$search%' OR s.name LIKE '%$search%')";
    }
    
    if (!empty($supplier_filter)) {
        $purchases_query .= " AND p.supplier_id = $supplier_filter";
    }
    
    if (!empty($status_filter)) {
        $purchases_query .= " AND p.payment_status = '$status_filter'";
    }
    
    if (!empty($date_from)) {
        $purchases_query .= " AND p.purchase_date >= '$date_from'";
    }
    
    if (!empty($date_to)) {
        $purchases_query .= " AND p.purchase_date <= '$date_to'";
    }
    
    $purchases_query .= " ORDER BY p.purchase_date DESC";
    $purchases_result = mysqli_query($conn, $purchases_query);
}

// معالجة إضافة مشتريات جديدة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_purchase'])) {
    // التحقق من وجود الجداول المطلوبة
    if (!$table_exists) {
        // إنشاء جدول المشتريات إذا لم يكن موجودًا
        $create_purchases_table_query = "CREATE TABLE purchases (
            id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
            supplier_id INT(11) NOT NULL,
            invoice_number VARCHAR(50) NOT NULL,
            purchase_date DATE NOT NULL,
            total_amount DECIMAL(10,2) NOT NULL,
            paid_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
            payment_status ENUM('paid', 'partial', 'unpaid') NOT NULL DEFAULT 'unpaid',
            payment_method VARCHAR(50) NULL,
            notes TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        if (mysqli_query($conn, $create_purchases_table_query)) {
            $table_exists = true;
        } else {
            $error_message = "فشل في إنشاء جدول المشتريات: " . mysqli_error($conn);
        }
    }
    
    if (!$suppliers_table_exists) {
        // إنشاء جدول الموردين إذا لم يكن موجودًا
        $create_suppliers_table_query = "CREATE TABLE suppliers (
            id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            phone VARCHAR(20) NULL,
            email VARCHAR(100) NULL,
            address TEXT NULL,
            notes TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        if (mysqli_query($conn, $create_suppliers_table_query)) {
            $suppliers_table_exists = true;
        } else {
            $error_message = "فشل في إنشاء جدول الموردين: " . mysqli_error($conn);
        }
    }
    
    // إذا تم إنشاء الجداول بنجاح، نستمر في إضافة المشتريات
    if ($table_exists && $suppliers_table_exists) {
        $supplier_id = intval($_POST['supplier_id']);
        $invoice_number = mysqli_real_escape_string($conn, $_POST['invoice_number']);
        $purchase_date = mysqli_real_escape_string($conn, $_POST['purchase_date']);
        $total_amount = floatval($_POST['total_amount']);
        $paid_amount = floatval($_POST['paid_amount']);
        $payment_method = mysqli_real_escape_string($conn, $_POST['payment_method']);
        $notes = mysqli_real_escape_string($conn, $_POST['notes']);
        
        // تحديد حالة الدفع
        $payment_status = 'unpaid';
        if ($paid_amount >= $total_amount) {
            $payment_status = 'paid';
            $paid_amount = $total_amount; // للتأكد من عدم تجاوز المبلغ المدفوع للمبلغ الإجمالي
        } elseif ($paid_amount > 0) {
            $payment_status = 'partial';
        }
    
        // إدخال بيانات المشتريات
        $insert_purchase_query = "INSERT INTO purchases (supplier_id, invoice_number, purchase_date, total_amount, paid_amount, payment_method, payment_status, notes) 
                                VALUES ($supplier_id, '$invoice_number', '$purchase_date', $total_amount, $paid_amount, '$payment_method', '$payment_status', '$notes')";
        
        if (mysqli_query($conn, $insert_purchase_query)) {
        $purchase_id = mysqli_insert_id($conn);
        
        // إذا كان هناك مبلغ مدفوع، قم بتسجيله في الخزنة
        if ($paid_amount > 0) {
            // التحقق من وجود جدول الخزنة
            $cashbox_table_exists_query = "SHOW TABLES LIKE 'cashbox'";
            $cashbox_table_exists_result = mysqli_query($conn, $cashbox_table_exists_query);
            $cashbox_table_exists = mysqli_num_rows($cashbox_table_exists_result) > 0;
            
            if (!$cashbox_table_exists) {
                // إنشاء جدول الخزنة إذا لم يكن موجودًا
                $create_cashbox_table_query = "CREATE TABLE cashbox (
                    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
                    transaction_type ENUM('income', 'expense') NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    transaction_date DATE NOT NULL,
                    description TEXT NULL,
                    reference_id INT(11) NULL,
                    reference_type VARCHAR(50) NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )";
                
                mysqli_query($conn, $create_cashbox_table_query);
                $cashbox_table_exists = true;
            }
            
            if ($cashbox_table_exists) {
                $cashbox_query = "INSERT INTO cashbox (transaction_type, amount, transaction_date, description, reference_id, reference_type) 
                                 VALUES ('expense', $paid_amount, '$purchase_date', 'دفعة مشتريات - فاتورة رقم $invoice_number', $purchase_id, 'purchase')";
                mysqli_query($conn, $cashbox_query);
            }
        }
        
        $success_message = "تم إضافة المشتريات بنجاح.";
        // إعادة تحميل الصفحة لتحديث البيانات
        header("Location: purchases.php?success=1");
        exit();
    } else {
        $error_message = "حدث خطأ أثناء إضافة المشتريات: " . mysqli_error($conn);
    }
    }
}

// معالجة تعديل مشتريات
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['edit_purchase']) && $table_exists) {
    $purchase_id = intval($_POST['purchase_id']);
    $supplier_id = intval($_POST['supplier_id']);
    $invoice_number = mysqli_real_escape_string($conn, $_POST['invoice_number']);
    $purchase_date = mysqli_real_escape_string($conn, $_POST['purchase_date']);
    $total_amount = floatval($_POST['total_amount']);
    $paid_amount = floatval($_POST['paid_amount']);
    $payment_method = mysqli_real_escape_string($conn, $_POST['payment_method']);
    $notes = mysqli_real_escape_string($conn, $_POST['notes']);
    
    // تحديد حالة الدفع
    $payment_status = 'unpaid';
    if ($paid_amount >= $total_amount) {
        $payment_status = 'paid';
        $paid_amount = $total_amount; // للتأكد من عدم تجاوز المبلغ المدفوع للمبلغ الإجمالي
    } elseif ($paid_amount > 0) {
        $payment_status = 'partial';
    }
    
    // تحديث بيانات المشتريات
    $update_purchase_query = "UPDATE purchases SET 
                             supplier_id = $supplier_id, 
                             invoice_number = '$invoice_number', 
                             purchase_date = '$purchase_date', 
                             total_amount = $total_amount, 
                             paid_amount = $paid_amount, 
                             payment_method = '$payment_method', 
                             payment_status = '$payment_status', 
                             notes = '$notes' 
                             WHERE id = $purchase_id";
    
    if (mysqli_query($conn, $update_purchase_query)) {
        // التحقق من وجود جدول الخزنة
        $cashbox_table_exists_query = "SHOW TABLES LIKE 'cashbox'";
        $cashbox_table_exists_result = mysqli_query($conn, $cashbox_table_exists_query);
        $cashbox_table_exists = mysqli_num_rows($cashbox_table_exists_result) > 0;
        
        if ($cashbox_table_exists) {
            // تحديث سجل الخزنة إذا كان هناك تغيير في المبلغ المدفوع
            $check_cashbox_query = "SELECT id, amount FROM cashbox WHERE reference_id = $purchase_id AND reference_type = 'purchase'";
            $check_cashbox_result = mysqli_query($conn, $check_cashbox_query);
            
            if ($check_cashbox_result && mysqli_num_rows($check_cashbox_result) > 0) {
                $cashbox_record = mysqli_fetch_assoc($check_cashbox_result);
                $cashbox_id = $cashbox_record['id'];
                $old_amount = $cashbox_record['amount'];
                
                if ($old_amount != $paid_amount) {
                    $update_cashbox_query = "UPDATE cashbox SET amount = $paid_amount WHERE id = $cashbox_id";
                    mysqli_query($conn, $update_cashbox_query);
                }
            } elseif ($paid_amount > 0) {
                $cashbox_query = "INSERT INTO cashbox (transaction_type, amount, transaction_date, description, reference_id, reference_type) 
                                 VALUES ('expense', $paid_amount, '$purchase_date', 'دفعة مشتريات - فاتورة رقم $invoice_number', $purchase_id, 'purchase')";
                mysqli_query($conn, $cashbox_query);
            }
        } elseif ($paid_amount > 0) {
            // إنشاء جدول الخزنة إذا لم يكن موجودًا
            $create_cashbox_table_query = "CREATE TABLE cashbox (
                id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
                transaction_type ENUM('income', 'expense') NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                transaction_date DATE NOT NULL,
                description TEXT NULL,
                reference_id INT(11) NULL,
                reference_type VARCHAR(50) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            
            if (mysqli_query($conn, $create_cashbox_table_query)) {
                $cashbox_query = "INSERT INTO cashbox (transaction_type, amount, transaction_date, description, reference_id, reference_type) 
                                 VALUES ('expense', $paid_amount, '$purchase_date', 'دفعة مشتريات - فاتورة رقم $invoice_number', $purchase_id, 'purchase')";
                mysqli_query($conn, $cashbox_query);
            }
        }
        
        $success_message = "تم تحديث المشتريات بنجاح.";
        // إعادة تحميل الصفحة لتحديث البيانات
        header("Location: purchases.php?success=2");
        exit();
    } else {
        $error_message = "حدث خطأ أثناء تحديث المشتريات: " . mysqli_error($conn);
    }
}

// معالجة حذف مشتريات
if (isset($_GET['delete']) && !empty($_GET['delete']) && $table_exists) {
    $purchase_id = intval($_GET['delete']);
    
    // التحقق من وجود جدول الخزنة
    $cashbox_table_exists_query = "SHOW TABLES LIKE 'cashbox'";
    $cashbox_table_exists_result = mysqli_query($conn, $cashbox_table_exists_query);
    $cashbox_table_exists = mysqli_num_rows($cashbox_table_exists_result) > 0;
    
    // حذف سجلات الخزنة المرتبطة بالمشتريات إذا كان الجدول موجودًا
    if ($cashbox_table_exists) {
        $delete_cashbox_query = "DELETE FROM cashbox WHERE reference_id = $purchase_id AND reference_type = 'purchase'";
        mysqli_query($conn, $delete_cashbox_query);
    }
    
    // حذف المشتريات
    $delete_purchase_query = "DELETE FROM purchases WHERE id = $purchase_id";
    if (mysqli_query($conn, $delete_purchase_query)) {
        $success_message = "تم حذف المشتريات بنجاح.";
        // إعادة تحميل الصفحة لتحديث البيانات
        header("Location: purchases.php?success=3");
        exit();
    } else {
        $error_message = "حدث خطأ أثناء حذف المشتريات: " . mysqli_error($conn);
    }
}

// معالجة إضافة دفعة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_payment'])) {
    $purchase_id = intval($_POST['purchase_id']);
    $amount = floatval($_POST['amount']);
    $payment_date = mysqli_real_escape_string($conn, $_POST['payment_date']);
    $payment_method = mysqli_real_escape_string($conn, $_POST['payment_method']);
    $notes = mysqli_real_escape_string($conn, $_POST['notes']);
    
    // الحصول على بيانات المشتريات الحالية
    $get_purchase_query = "SELECT total_amount, paid_amount, invoice_number FROM purchases WHERE id = $purchase_id";
    $get_purchase_result = mysqli_query($conn, $get_purchase_query);
    $purchase_data = mysqli_fetch_assoc($get_purchase_result);
    
    $total_amount = $purchase_data['total_amount'];
    $current_paid = $purchase_data['paid_amount'];
    $invoice_number = $purchase_data['invoice_number'];
    
    $new_paid_amount = $current_paid + $amount;
    
    // تحديد حالة الدفع الجديدة
    $payment_status = 'partial';
    if ($new_paid_amount >= $total_amount) {
        $payment_status = 'paid';
        $new_paid_amount = $total_amount; // للتأكد من عدم تجاوز المبلغ المدفوع للمبلغ الإجمالي
    }
    
    // تحديث بيانات المشتريات
    $update_purchase_query = "UPDATE purchases SET 
                             paid_amount = $new_paid_amount, 
                             payment_status = '$payment_status' 
                             WHERE id = $purchase_id";
    
    if (mysqli_query($conn, $update_purchase_query)) {
        // تسجيل الدفعة في الخزنة
        $cashbox_query = "INSERT INTO cashbox (transaction_type, amount, transaction_date, description, reference_id, reference_type) 
                         VALUES ('expense', $amount, '$payment_date', 'دفعة مشتريات - فاتورة رقم $invoice_number', $purchase_id, 'purchase_payment')";
        mysqli_query($conn, $cashbox_query);
        
        $success_message = "تم إضافة الدفعة بنجاح.";
        // إعادة تحميل الصفحة لتحديث البيانات
        header("Location: purchases.php?success=4");
        exit();
    } else {
        $error_message = "حدث خطأ أثناء إضافة الدفعة: " . mysqli_error($conn);
    }
}

// رسائل النجاح
if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case 1:
            $success_message = "تم إضافة المشتريات بنجاح.";
            break;
        case 2:
            $success_message = "تم تحديث المشتريات بنجاح.";
            break;
        case 3:
            $success_message = "تم حذف المشتريات بنجاح.";
            break;
        case 4:
            $success_message = "تم إضافة الدفعة بنجاح.";
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المشتريات - نظام إدارة المتجر</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --sidebar-width: 250px;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fb;
            color: #333;
            overflow-x: hidden;
        }
        
        /* تصميم الشريط الجانبي */
        .sidebar {
            height: 100vh;
            width: var(--sidebar-width);
            position: fixed;
            top: 0;
            right: 0;
            background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding-top: 20px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            z-index: 1000;
        }
        
        .sidebar .logo {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .logo h3 {
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 15px;
            border-radius: 8px;
            transition: all 0.3s;
            font-size: 16px;
        }
        
        .sidebar .nav-link:hover, 
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar .nav-link i {
            margin-left: 10px;
            width: 25px;
            text-align: center;
        }
        
        /* تصميم المحتوى الرئيسي */
        .main-content {
            margin-right: var(--sidebar-width);
            padding: 20px;
            transition: all 0.3s;
        }
        
        /* شريط التنقل العلوي */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        
        .search-bar {
            background-color: #f8f9fa;
            border-radius: 30px;
            padding: 8px 20px;
            border: 1px solid #eaeaea;
        }
        
        .search-bar input {
            background: transparent;
            border: none;
            width: 100%;
            padding: 5px 10px;
        }
        
        .search-bar input:focus {
            outline: none;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #f94144;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* بطاقات الإحصائيات */
        .stat-card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .stat-card .card-title {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-card .card-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .stat-card .card-footer {
            background: transparent;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 13px;
            padding: 8px 15px;
        }
        
        /* تصميم قسم المشتريات */
        .purchases-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .purchase-filters {
            margin-bottom: 20px;
        }
        
        .purchase-table .payment-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .purchase-table .payment-status.paid {
            background-color: #e6f4ea;
            color: #0f9d58;
        }
        
        .purchase-table .payment-status.partial {
            background-color: #fef7e0;
            color: #f9ab00;
        }
        
        .purchase-table .payment-status.unpaid {
            background-color: #fce8e6;
            color: #ea4335;
        }
        
        .purchase-actions .btn {
            padding: 5px 10px;
            font-size: 14px;
        }
        
        /* تصميم النموذج */
        .modal-header {
            background-color: var(--primary-color);
            color: white;
        }
        
        .modal-footer {
            background-color: #f8f9fa;
        }
        
        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
                overflow: hidden;
            }
            
            .sidebar .logo h3, .sidebar .nav-link span {
                display: none;
            }
            
            .sidebar .nav-link {
                text-align: center;
                margin: 5px 10px;
            }
            
            .sidebar .nav-link i {
                margin-left: 0;
            }
            
            .main-content {
                margin-right: 70px;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-store"></i> متجرنا</h3>
        </div>
        <ul class="nav flex-column mt-4">
            <li class="nav-item">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales.php">
                    <i class="fas fa-shopping-cart"></i>
                    <span>المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales_management.php">
                    <i class="fas fa-chart-line"></i>
                    <span>إدارة المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="products.php">
                    <i class="fas fa-box"></i>
                    <span>المنتجات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customers.php">
                    <i class="fas fa-users"></i>
                    <span>العملاء</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="suppliers.php">
                    <i class="fas fa-truck"></i>
                    <span>الموردين</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="purchases.php">
                    <i class="fas fa-shopping-basket"></i>
                    <span>المشتريات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="expenses.php">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>المصروفات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="cashbox.php">
                    <i class="fas fa-cash-register"></i>
                    <span>الخزنة</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="financial.php">
                    <i class="fas fa-chart-pie"></i>
                    <span>الإدارة المالية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="barcode.php">
                    <i class="fas fa-barcode"></i>
                    <span>مولد الباركود</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="settings.php">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط التنقل العلوي -->
        <div class="top-navbar">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4 class="mb-0">إدارة المشتريات</h4>
                    <p class="mb-0 text-muted">مرحباً بعودتك، <?php echo $_SESSION['username']; ?>!</p>
                </div>
                <div class="col-md-6 d-flex justify-content-end">
                    <div class="d-flex align-items-center">
                        <div class="search-bar me-3">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="بحث...">
                        </div>
                        <div class="position-relative me-3">
                            <button class="btn btn-light rounded-circle">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-light rounded-circle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.php">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- عرض رسائل النجاح والخطأ -->
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <!-- بطاقات الإحصائيات -->
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-primary-subtle text-primary me-3">
                            <i class="fas fa-shopping-basket"></i>
                        </div>
                        <div>
                            <div class="card-title">إجمالي المشتريات</div>
                            <div class="card-value"><?php echo number_format($total_purchases_count); ?></div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-money-bill-wave"></i> <?php echo number_format($total_purchases_amount, 2); ?> ر.س
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-danger-subtle text-danger me-3">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div>
                            <div class="card-title">مشتريات غير مدفوعة</div>
                            <div class="card-value"><?php echo number_format($unpaid_purchases_count); ?></div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-money-bill-wave"></i> <?php echo number_format($unpaid_purchases_amount, 2); ?> ر.س
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-success-subtle text-success me-3">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div>
                            <div class="card-title">مشتريات حديثة</div>
                            <div class="card-value"><?php echo number_format($recent_purchases_count); ?></div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-clock"></i> آخر 30 يوم
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-warning-subtle text-warning me-3">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div>
                            <div class="card-title">الموردين</div>
                            <div class="card-value"><?php echo number_format(count($suppliers)); ?></div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <a href="suppliers.php" class="text-decoration-none">
                            <i class="fas fa-external-link-alt"></i> عرض الموردين
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم المشتريات -->
        <div class="purchases-container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0">قائمة المشتريات</h5>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPurchaseModal">
                        <i class="fas fa-plus me-1"></i> إضافة مشتريات
                    </button>
                </div>
            </div>
            
            <!-- فلاتر البحث -->
            <div class="purchase-filters">
                <form action="" method="get" class="row g-3">
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" name="search" placeholder="بحث برقم الفاتورة أو المورد" value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="supplier">
                            <option value="">جميع الموردين</option>
                            <?php foreach ($suppliers as $supplier): ?>
                                <option value="<?php echo $supplier['id']; ?>" <?php echo ($supplier_filter == $supplier['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($supplier['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="paid" <?php echo ($status_filter == 'paid') ? 'selected' : ''; ?>>مدفوعة</option>
                            <option value="partial" <?php echo ($status_filter == 'partial') ? 'selected' : ''; ?>>مدفوعة جزئياً</option>
                            <option value="unpaid" <?php echo ($status_filter == 'unpaid') ? 'selected' : ''; ?>>غير مدفوعة</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" name="date_from" placeholder="من تاريخ" value="<?php echo $date_from; ?>">
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" name="date_to" placeholder="إلى تاريخ" value="<?php echo $date_to; ?>">
                    </div>
                    <div class="col-md-1">
                        <button type="submit" class="btn btn-primary w-100">بحث</button>
                    </div>
                </form>
            </div>
            
            <!-- جدول المشتريات -->
            <div class="purchase-table">
                <div class="table-responsive">
                    <table class="table table-hover" id="purchasesTable">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>رقم الفاتورة</th>
                                <th>المورد</th>
                                <th>التاريخ</th>
                                <th>المبلغ الإجمالي</th>
                                <th>المبلغ المدفوع</th>
                                <th>المبلغ المتبقي</th>
                                <th>طريقة الدفع</th>
                                <th>حالة الدفع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            if ($purchases_result && mysqli_num_rows($purchases_result) > 0) {
                                $counter = 1;
                                while ($purchase = mysqli_fetch_assoc($purchases_result)) {
                                    $remaining = $purchase['total_amount'] - $purchase['paid_amount'];
                                    
                                    // تحديد حالة الدفع
                                    $status_class = '';
                                    $status_text = '';
                                    
                                    switch ($purchase['payment_status']) {
                                        case 'paid':
                                            $status_class = 'paid';
                                            $status_text = 'مدفوعة';
                                            break;
                                        case 'partial':
                                            $status_class = 'partial';
                                            $status_text = 'مدفوعة جزئياً';
                                            break;
                                        case 'unpaid':
                                            $status_class = 'unpaid';
                                            $status_text = 'غير مدفوعة';
                                            break;
                                    }
                                    
                                    echo '<tr>';
                                    echo '<td>' . $counter++ . '</td>';
                                    echo '<td>' . htmlspecialchars($purchase['invoice_number']) . '</td>';
                                    echo '<td>' . htmlspecialchars($purchase['supplier_name']) . '</td>';
                                    echo '<td>' . date('Y-m-d', strtotime($purchase['purchase_date'])) . '</td>';
                                    echo '<td>' . number_format($purchase['total_amount'], 2) . ' ر.س</td>';
                                    echo '<td>' . number_format($purchase['paid_amount'], 2) . ' ر.س</td>';
                                    echo '<td>' . number_format($remaining, 2) . ' ر.س</td>';
                                    echo '<td>' . htmlspecialchars(getPaymentMethodText($purchase['payment_method'])) . '</td>';
                                    echo '<td><span class="payment-status ' . $status_class . '">' . $status_text . '</span></td>';
                                    echo '<td class="purchase-actions">';
                                    echo '<button class="btn btn-sm btn-info me-1" onclick="editPurchase(' . $purchase['id'] . ')" data-bs-toggle="modal" data-bs-target="#editPurchaseModal"><i class="fas fa-edit"></i></button>';
                                    
                                    if ($purchase['payment_status'] != 'paid') {
                                        echo '<button class="btn btn-sm btn-success me-1" onclick="addPayment(' . $purchase['id'] . ', \'' . htmlspecialchars($purchase['invoice_number']) . '\', ' . $remaining . ')" data-bs-toggle="modal" data-bs-target="#addPaymentModal"><i class="fas fa-money-bill-wave"></i></button>';
                                    }
                                    
                                    echo '<a href="purchase_details.php?id=' . $purchase['id'] . '" class="btn btn-sm btn-primary me-1"><i class="fas fa-eye"></i></a>';
                                    echo '<button class="btn btn-sm btn-danger" onclick="confirmDelete(' . $purchase['id'] . ')"><i class="fas fa-trash"></i></button>';
                                    echo '</td>';
                                    echo '</tr>';
                                }
                            } else {
                                echo '<tr><td colspan="10" class="text-center">لا توجد مشتريات متاحة</td></tr>';
                            }
                            
                            // دالة لتحويل رمز طريقة الدفع إلى نص
                            function getPaymentMethodText($method) {
                                switch ($method) {
                                    case 'cash':
                                        return 'نقدي';
                                    case 'bank_transfer':
                                        return 'تحويل بنكي';
                                    case 'check':
                                        return 'شيك';
                                    case 'credit_card':
                                        return 'بطاقة ائتمان';
                                    default:
                                        return $method;
                                }
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال إضافة مشتريات جديدة -->
    <div class="modal fade" id="addPurchaseModal" tabindex="-1" aria-labelledby="addPurchaseModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addPurchaseModalLabel">إضافة مشتريات جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addPurchaseForm" action="" method="post">
                        <input type="hidden" name="add_purchase" value="1">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="supplier_id" class="form-label">المورد <span class="text-danger">*</span></label>
                                <select class="form-select" id="supplier_id" name="supplier_id" required>
                                    <option value="">اختر المورد</option>
                                    <?php foreach ($suppliers as $supplier): ?>
                                        <option value="<?php echo $supplier['id']; ?>"><?php echo htmlspecialchars($supplier['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="invoice_number" class="form-label">رقم الفاتورة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="invoice_number" name="invoice_number" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="purchase_date" class="form-label">تاريخ الشراء <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="purchase_date" name="purchase_date" value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="total_amount" class="form-label">المبلغ الإجمالي <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="total_amount" name="total_amount" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="paid_amount" class="form-label">المبلغ المدفوع</label>
                                <input type="number" class="form-control" id="paid_amount" name="paid_amount" step="0.01" min="0" value="0">
                            </div>
                            <div class="col-md-6">
                                <label for="payment_method" class="form-label">طريقة الدفع</label>
                                <select class="form-select" id="payment_method" name="payment_method">
                                    <option value="cash">نقدي</option>
                                    <option value="bank_transfer">تحويل بنكي</option>
                                    <option value="check">شيك</option>
                                    <option value="credit_card">بطاقة ائتمان</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="addPurchaseForm" class="btn btn-primary">إضافة المشتريات</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال تعديل مشتريات -->
    <div class="modal fade" id="editPurchaseModal" tabindex="-1" aria-labelledby="editPurchaseModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editPurchaseModalLabel">تعديل مشتريات</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editPurchaseForm" action="" method="post">
                        <input type="hidden" name="edit_purchase" value="1">
                        <input type="hidden" name="purchase_id" id="edit_purchase_id">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_supplier_id" class="form-label">المورد <span class="text-danger">*</span></label>
                                <select class="form-select" id="edit_supplier_id" name="supplier_id" required>
                                    <option value="">اختر المورد</option>
                                    <?php foreach ($suppliers as $supplier): ?>
                                        <option value="<?php echo $supplier['id']; ?>"><?php echo htmlspecialchars($supplier['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_invoice_number" class="form-label">رقم الفاتورة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_invoice_number" name="invoice_number" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_purchase_date" class="form-label">تاريخ الشراء <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="edit_purchase_date" name="purchase_date" required>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_total_amount" class="form-label">المبلغ الإجمالي <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="edit_total_amount" name="total_amount" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_paid_amount" class="form-label">المبلغ المدفوع</label>
                                <input type="number" class="form-control" id="edit_paid_amount" name="paid_amount" step="0.01" min="0">
                            </div>
                            <div class="col-md-6">
                                <label for="edit_payment_method" class="form-label">طريقة الدفع</label>
                                <select class="form-select" id="edit_payment_method" name="payment_method">
                                    <option value="cash">نقدي</option>
                                    <option value="bank_transfer">تحويل بنكي</option>
                                    <option value="check">شيك</option>
                                    <option value="credit_card">بطاقة ائتمان</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="edit_notes" name="notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="editPurchaseForm" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال إضافة دفعة -->
    <div class="modal fade" id="addPaymentModal" tabindex="-1" aria-labelledby="addPaymentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addPaymentModalLabel">إضافة دفعة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addPaymentForm" action="" method="post">
                        <input type="hidden" name="add_payment" value="1">
                        <input type="hidden" name="purchase_id" id="payment_purchase_id">
                        <div class="mb-3">
                            <label for="invoice_number_display" class="form-label">رقم الفاتورة</label>
                            <input type="text" class="form-control" id="invoice_number_display" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="remaining_amount" class="form-label">المبلغ المتبقي</label>
                            <input type="text" class="form-control" id="remaining_amount" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="amount" class="form-label">مبلغ الدفعة <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0" required>
                        </div>
                        <div class="mb-3">
                            <label for="payment_date" class="form-label">تاريخ الدفع <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="payment_date" name="payment_date" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="payment_method_add" class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                            <select class="form-select" id="payment_method_add" name="payment_method" required>
                                <option value="cash">نقدي</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                                <option value="credit_card">بطاقة ائتمان</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="payment_notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="payment_notes" name="notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="addPaymentForm" class="btn btn-primary">إضافة الدفعة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        // دالة مساعدة لتهيئة DataTables مع معالجة الأخطاء
        function initializeDataTable(tableId, expectedColumns, actionColumnIndex) {
            // التأكد من تحميل DataTables بشكل صحيح
            if (typeof $.fn.DataTable === 'undefined') {
                console.error('DataTables library not loaded properly');
                $(tableId).addClass('table-striped table-bordered');
                return false;
            }

            try {
                // التحقق من وجود الجدول
                if ($(tableId).length === 0) {
                    console.error('Table with ID ' + tableId + ' not found');
                    return false;
                }

                // التحقق من عدد الأعمدة قبل تهيئة DataTable
                var columnCount = $(tableId + ' thead tr th').length;
                var bodyRowCount = $(tableId + ' tbody tr').length;
                console.log(tableId + ' - Number of columns detected:', columnCount);
                console.log(tableId + ' - Number of body rows:', bodyRowCount);

                // التحقق من أن الجدول يحتوي على الأعمدة المطلوبة
                if (columnCount !== expectedColumns) {
                    console.warn('Expected ' + expectedColumns + ' columns for ' + tableId + ', but found:', columnCount);
                }

                // التحقق من تطابق عدد الأعمدة في كل صف
                var isTableValid = true;
                $(tableId + ' tbody tr').each(function(index, row) {
                    var cellCount = $(row).find('td').length;
                    if (cellCount !== columnCount && cellCount !== 1) { // السماح بصف واحد للرسالة "لا توجد بيانات"
                        console.warn(tableId + ' - Row ' + index + ' has ' + cellCount + ' cells, expected ' + columnCount);
                        isTableValid = false;
                    }
                });

                if (!isTableValid) {
                    console.error(tableId + ' structure is invalid - column count mismatch detected');
                    
                    // محاولة إصلاح الجدول
                    $(tableId + ' tbody tr').each(function(index, row) {
                        var cellCount = $(row).find('td').length;
                        if (cellCount > columnCount) {
                            // إذا كان عدد الخلايا أكبر من عدد الأعمدة، قم بدمج الخلايا الزائدة
                            var cells = $(row).find('td');
                            var lastCell = cells.eq(columnCount - 1);
                            var content = lastCell.html() || '';
                            
                            for (var i = columnCount; i < cellCount; i++) {
                                content += ' ' + cells.eq(i).html();
                                cells.eq(i).remove();
                            }
                            
                            lastCell.html(content);
                        } else if (cellCount < columnCount && cellCount > 1) {
                            // إذا كان عدد الخلايا أقل من عدد الأعمدة، أضف خلايا فارغة
                            for (var i = cellCount; i < columnCount; i++) {
                                $(row).append('<td></td>');
                            }
                        }
                    });
                    
                    console.log('Table structure fixed');
                }

                // تدمير أي نسخة سابقة من DataTable
                if ($.fn.DataTable.isDataTable(tableId)) {
                    $(tableId).DataTable().destroy();
                }

                // إعادة تهيئة الجدول
                var dataTable = $(tableId).DataTable({
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
                    },
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "الكل"]],
                    "columnDefs": [
                        { "orderable": false, "targets": [actionColumnIndex] }, // جعل عمود الإجراءات غير قابل للترتيب
                        { "targets": "_all", "className": "text-center" }
                    ],
                    "retrieve": false,
                    "paging": true,
                    "ordering": true,
                    "info": true,
                    "autoWidth": false,
                    "searching": true,
                    "responsive": true,
                    "destroy": true, // إضافة هذا الخيار لإعادة تهيئة الجدول إذا لزم الأمر
                    "deferRender": true, // تحسين الأداء للجداول الكبيرة
                    "stateSave": false, // تعطيل حفظ الحالة لتجنب مشاكل التخزين المؤقت
                    "processing": false, // تعطيل رسالة المعالجة
                    "dom": 'Bfrtip', // تحديد مكونات واجهة المستخدم
                    "buttons": [] // لا توجد أزرار إضافية
                });

                console.log(tableId + ' DataTable initialized successfully');
                return true;

            } catch (e) {
                console.error("Error initializing " + tableId + " DataTable:", e);
                console.error("Column count:", $(tableId + ' thead tr th').length);
                console.error("Row count:", $(tableId + ' tbody tr').length);

                // تحليل تفصيلي للخطأ
                $(tableId + ' tbody tr').each(function(index, row) {
                    var cellCount = $(row).find('td').length;
                    console.log(tableId + ' - Row ' + index + ' has ' + cellCount + ' cells');
                });

                // Fallback إلى جدول أساسي إذا فشل DataTables
                $(tableId).addClass('table-striped table-bordered');

                // إضافة تنسيق أساسي للجدول
                $(tableId).css({
                    'width': '100%',
                    'border-collapse': 'collapse'
                });

                // عرض رسالة خطأ للمستخدم
                if (e.message && e.message.includes('Incorrect column count')) {
                    console.warn(tableId + ' DataTables column count error detected - using basic table instead');
                    // إضافة رسالة تحذير في أعلى الجدول
                    $(tableId).before('<div class="alert alert-warning alert-dismissible fade show" role="alert">' +
                        'تحذير: تم اكتشاف مشكلة في بنية الجدول. يتم عرض الجدول في الوضع الأساسي.' +
                        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                        '</div>');
                }
                return false;
            }
        }

        // تهيئة جدول المشتريات
        $(document).ready(function() {
            // استخدام الدالة المساعدة لتهيئة جدول المشتريات
            initializeDataTable('#purchasesTable', 10, 9);
            
            // التحقق من المبلغ المدفوع
            $('#total_amount, #paid_amount').on('input', function() {
                validatePaidAmount();
            });
            
            $('#edit_total_amount, #edit_paid_amount').on('input', function() {
                validateEditPaidAmount();
            });
            
            $('#amount').on('input', function() {
                validatePaymentAmount();
            });
        });
        
        // التحقق من أن المبلغ المدفوع لا يتجاوز المبلغ الإجمالي
        function validatePaidAmount() {
            const totalAmount = parseFloat($('#total_amount').val()) || 0;
            const paidAmount = parseFloat($('#paid_amount').val()) || 0;
            
            if (paidAmount > totalAmount) {
                $('#paid_amount').val(totalAmount.toFixed(2));
            }
        }
        
        function validateEditPaidAmount() {
            const totalAmount = parseFloat($('#edit_total_amount').val()) || 0;
            const paidAmount = parseFloat($('#edit_paid_amount').val()) || 0;
            
            if (paidAmount > totalAmount) {
                $('#edit_paid_amount').val(totalAmount.toFixed(2));
            }
        }
        
        function validatePaymentAmount() {
            const remainingAmount = parseFloat($('#remaining_amount').val().replace(/[^\d.-]/g, '')) || 0;
            const amount = parseFloat($('#amount').val()) || 0;
            
            if (amount > remainingAmount) {
                $('#amount').val(remainingAmount.toFixed(2));
            }
        }
        
        // دالة لتعديل المشتريات
        function editPurchase(purchaseId) {
            // هنا يمكن إضافة كود AJAX لجلب بيانات المشتريات من الخادم
            // لأغراض العرض، سنستخدم بيانات وهمية
            
            // في التطبيق الحقيقي، يجب استبدال هذا بطلب AJAX
            fetch('get_purchase.php?id=' + purchaseId)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('edit_purchase_id').value = data.id;
                    document.getElementById('edit_supplier_id').value = data.supplier_id;
                    document.getElementById('edit_invoice_number').value = data.invoice_number;
                    document.getElementById('edit_purchase_date').value = data.purchase_date.split(' ')[0];
                    document.getElementById('edit_total_amount').value = data.total_amount;
                    document.getElementById('edit_paid_amount').value = data.paid_amount;
                    document.getElementById('edit_payment_method').value = data.payment_method;
                    document.getElementById('edit_notes').value = data.notes;
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء جلب بيانات المشتريات');
                });
        }
        
        // دالة لإضافة دفعة
        function addPayment(purchaseId, invoiceNumber, remainingAmount) {
            document.getElementById('payment_purchase_id').value = purchaseId;
            document.getElementById('invoice_number_display').value = invoiceNumber;
            document.getElementById('remaining_amount').value = remainingAmount.toFixed(2) + ' ر.س';
            document.getElementById('amount').value = remainingAmount.toFixed(2);
            document.getElementById('amount').max = remainingAmount;
        }
        
        // دالة لتأكيد حذف المشتريات
        function confirmDelete(purchaseId) {
            if (confirm('هل أنت متأكد من رغبتك في حذف هذه المشتريات؟')) {
                window.location.href = 'purchases.php?delete=' + purchaseId;
            }
        }
    </script>
</body>
</html>