<?php
// بدء جلسة
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// التحقق من صلاحيات المستخدم (يجب أن يكون مدير)
if ($_SESSION['role'] !== 'admin') {
    header("Location: dashboard.php?error=unauthorized");
    exit();
}

// استيراد ملف الاتصال بقاعدة البيانات
require_once 'config/db_connection.php';

// تهيئة مصفوفة المستخدمين
$users = [];

// التحقق من وجود جدول المستخدمين
$users_table_exists_query = "SHOW TABLES LIKE 'users'";
$users_table_exists_result = mysqli_query($conn, $users_table_exists_query);
$users_table_exists = mysqli_num_rows($users_table_exists_result) > 0;

// استعلام لجلب بيانات المستخدمين إذا كان الجدول موجودًا
if ($users_table_exists) {
    $users_query = "SELECT id, username, name, email, role, last_login FROM users ORDER BY id";
    $users_result = mysqli_query($conn, $users_query);
    if ($users_result) {
        while ($user = mysqli_fetch_assoc($users_result)) {
            $users[] = $user;
        }
    }
}

// معالجة إضافة مستخدم جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_user'])) {
    $username = mysqli_real_escape_string($conn, $_POST['username']);
    $name = mysqli_real_escape_string($conn, $_POST['name']);
    $email = !empty($_POST['email']) ? mysqli_real_escape_string($conn, $_POST['email']) : null;
    $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
    $role = mysqli_real_escape_string($conn, $_POST['role']);
    
    // التحقق من وجود جدول المستخدمين
    if (!$users_table_exists) {
        // إنشاء جدول المستخدمين إذا لم يكن موجودًا
        $create_users_table_query = "CREATE TABLE users (
            id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'cashier', 'manager') NOT NULL DEFAULT 'cashier',
            last_login DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        if (mysqli_query($conn, $create_users_table_query)) {
            $users_table_exists = true;
        } else {
            $error_message = "فشل في إنشاء جدول المستخدمين: " . mysqli_error($conn);
        }
    }
    
    if ($users_table_exists) {
        // التحقق من عدم تكرار اسم المستخدم
        $check_username_query = "SELECT id FROM users WHERE username = '$username'";
        $check_username_result = mysqli_query($conn, $check_username_query);
        
        if ($check_username_result && mysqli_num_rows($check_username_result) > 0) {
            $error_message = "اسم المستخدم مستخدم بالفعل، يرجى اختيار اسم مستخدم آخر.";
        } else {
            // إدخال بيانات المستخدم الجديد
            $insert_user_query = "INSERT INTO users (username, name, email, password, role) 
                                 VALUES ('$username', '$name', " . ($email ? "'$email'" : "NULL") . ", '$password', '$role')";
            
            if (mysqli_query($conn, $insert_user_query)) {
                $success_message = "تم إضافة المستخدم بنجاح.";
                // إعادة تحميل الصفحة لتحديث البيانات
            header("Location: settings.php?success=1");
            exit();
        } else {
            $error_message = "حدث خطأ أثناء إضافة المستخدم: " . mysqli_error($conn);
        }
    }
}

// معالجة تعديل مستخدم
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['edit_user']) && $users_table_exists) {
    $user_id = intval($_POST['user_id']);
    $name = mysqli_real_escape_string($conn, $_POST['name']);
    $email = !empty($_POST['email']) ? mysqli_real_escape_string($conn, $_POST['email']) : null;
    $role = mysqli_real_escape_string($conn, $_POST['role']);
    
    // تحديث بيانات المستخدم
    $update_user_query = "UPDATE users SET 
                         name = '$name', 
                         email = " . ($email ? "'$email'" : "NULL") . ", 
                         role = '$role' 
                         WHERE id = $user_id";
    
    if (mysqli_query($conn, $update_user_query)) {
        // إذا تم تغيير كلمة المرور
        if (!empty($_POST['password'])) {
            $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
            $update_password_query = "UPDATE users SET password = '$password' WHERE id = $user_id";
            mysqli_query($conn, $update_password_query);
        }
        
        $success_message = "تم تحديث المستخدم بنجاح.";
        // إعادة تحميل الصفحة لتحديث البيانات
        header("Location: settings.php?success=2");
        exit();
    } else {
        $error_message = "حدث خطأ أثناء تحديث المستخدم: " . mysqli_error($conn);
    }
}

// معالجة حذف مستخدم
if (isset($_GET['delete_user']) && !empty($_GET['delete_user']) && $users_table_exists) {
    $user_id = intval($_GET['delete_user']);
    
    // التأكد من عدم حذف المستخدم الحالي
    if ($user_id == $_SESSION['user_id']) {
        $error_message = "لا يمكن حذف المستخدم الحالي.";
    } else {
        $delete_user_query = "DELETE FROM users WHERE id = $user_id";
        if (mysqli_query($conn, $delete_user_query)) {
            $success_message = "تم حذف المستخدم بنجاح.";
            // إعادة تحميل الصفحة لتحديث البيانات
            header("Location: settings.php?success=3");
            exit();
        } else {
            $error_message = "حدث خطأ أثناء حذف المستخدم: " . mysqli_error($conn);
        }
    }
}

// معالجة تحديث إعدادات المتجر
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_store_settings'])) {
    $store_name = mysqli_real_escape_string($conn, $_POST['store_name']);
    $store_phone = mysqli_real_escape_string($conn, $_POST['store_phone']);
    $store_address = mysqli_real_escape_string($conn, $_POST['store_address']);
    $tax_rate = floatval($_POST['tax_rate']);
    $invoice_footer = mysqli_real_escape_string($conn, $_POST['invoice_footer']);
    
    // التحقق من وجود جدول إعدادات المتجر
    $store_settings_table_exists_query = "SHOW TABLES LIKE 'store_settings'";
    $store_settings_table_exists_result = mysqli_query($conn, $store_settings_table_exists_query);
    $store_settings_table_exists = mysqli_num_rows($store_settings_table_exists_result) > 0;
    
    if (!$store_settings_table_exists) {
        // إنشاء جدول إعدادات المتجر إذا لم يكن موجودًا
        $create_store_settings_table_query = "CREATE TABLE store_settings (
            id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
            store_name VARCHAR(100) NOT NULL,
            store_phone VARCHAR(20) NULL,
            store_address TEXT NULL,
            tax_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
            invoice_footer TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        if (mysqli_query($conn, $create_store_settings_table_query)) {
            $store_settings_table_exists = true;
        } else {
            $error_message = "فشل في إنشاء جدول إعدادات المتجر: " . mysqli_error($conn);
        }
    }
    
    if ($store_settings_table_exists) {
        // التحقق من وجود إعدادات سابقة
        $check_settings_query = "SELECT id FROM store_settings LIMIT 1";
        $check_settings_result = mysqli_query($conn, $check_settings_query);
        
        if ($check_settings_result && mysqli_num_rows($check_settings_result) > 0) {
            // تحديث الإعدادات الموجودة
            $settings_id = mysqli_fetch_assoc($check_settings_result)['id'];
            $update_settings_query = "UPDATE store_settings SET 
                                     store_name = '$store_name', 
                                     store_phone = '$store_phone', 
                                     store_address = '$store_address', 
                                     tax_rate = $tax_rate, 
                                     invoice_footer = '$invoice_footer' 
                                     WHERE id = $settings_id";
            
            if (mysqli_query($conn, $update_settings_query)) {
                $success_message = "تم تحديث إعدادات المتجر بنجاح.";
                // إعادة تحميل الصفحة لتحديث البيانات
                header("Location: settings.php?success=4");
                exit();
            } else {
                $error_message = "حدث خطأ أثناء تحديث إعدادات المتجر: " . mysqli_error($conn);
            }
        } else {
            // إدخال إعدادات جديدة
            $insert_settings_query = "INSERT INTO store_settings (store_name, store_phone, store_address, tax_rate, invoice_footer) 
                                    VALUES ('$store_name', '$store_phone', '$store_address', $tax_rate, '$invoice_footer')";
            
            if (mysqli_query($conn, $insert_settings_query)) {
                $success_message = "تم حفظ إعدادات المتجر بنجاح.";
                // إعادة تحميل الصفحة لتحديث البيانات
                header("Location: settings.php?success=4");
                exit();
            } else {
                $error_message = "حدث خطأ أثناء حفظ إعدادات المتجر: " . mysqli_error($conn);
            }
        }
    }
}

// تهيئة إعدادات المتجر الافتراضية
$store_settings = [
    'store_name' => 'متجرنا',
    'store_phone' => '',
    'store_address' => '',
    'tax_rate' => 15,
    'invoice_footer' => 'شكراً لتسوقكم معنا'
];

// التحقق من وجود جدول إعدادات المتجر
$store_settings_table_exists_query = "SHOW TABLES LIKE 'store_settings'";
$store_settings_table_exists_result = mysqli_query($conn, $store_settings_table_exists_query);
$store_settings_table_exists = mysqli_num_rows($store_settings_table_exists_result) > 0;

// جلب إعدادات المتجر الحالية إذا كان الجدول موجودًا
if ($store_settings_table_exists) {
    $store_settings_query = "SELECT * FROM store_settings LIMIT 1";
    $store_settings_result = mysqli_query($conn, $store_settings_query);
    if ($store_settings_result && mysqli_num_rows($store_settings_result) > 0) {
        $store_settings = mysqli_fetch_assoc($store_settings_result);
    }
}

// معالجة النسخ الاحتياطي لقاعدة البيانات
if (isset($_GET['backup_db'])) {
    // تنفيذ النسخ الاحتياطي (هذا مجرد مثال، يجب تعديله حسب بيئة الخادم)
    $backup_file = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
    $backup_path = 'backups/' . $backup_file;
    
    // إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجودًا
    if (!file_exists('backups')) {
        mkdir('backups', 0777, true);
    }
    
    // أمر النسخ الاحتياطي (يختلف حسب نظام التشغيل وإعدادات الخادم)
    $command = "mysqldump -u " . DB_USER . " -p" . DB_PASS . " " . DB_NAME . " > " . $backup_path;
    
    // تنفيذ الأمر (هذا مجرد مثال، قد لا يعمل في جميع البيئات)
    // exec($command, $output, $return_var);
    
    // بدلاً من تنفيذ الأمر، سنقوم بإنشاء ملف فارغ للتوضيح
    file_put_contents($backup_path, '-- هذا ملف نسخ احتياطي تجريبي');
    
    $success_message = "تم إنشاء نسخة احتياطية بنجاح: " . $backup_file;
}

// رسائل النجاح
if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case 1:
            $success_message = "تم إضافة المستخدم بنجاح.";
            break;
        case 2:
            $success_message = "تم تحديث المستخدم بنجاح.";
            break;
        case 3:
            $success_message = "تم حذف المستخدم بنجاح.";
            break;
        case 4:
            $success_message = "تم تحديث إعدادات المتجر بنجاح.";
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - نظام إدارة المتجر</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --sidebar-width: 250px;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fb;
            color: #333;
            overflow-x: hidden;
        }
        
        /* تصميم الشريط الجانبي */
        .sidebar {
            height: 100vh;
            width: var(--sidebar-width);
            position: fixed;
            top: 0;
            right: 0;
            background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding-top: 20px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            z-index: 1000;
        }
        
        .sidebar .logo {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .logo h3 {
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 15px;
            border-radius: 8px;
            transition: all 0.3s;
            font-size: 16px;
        }
        
        .sidebar .nav-link:hover, 
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar .nav-link i {
            margin-left: 10px;
            width: 25px;
            text-align: center;
        }
        
        /* تصميم المحتوى الرئيسي */
        .main-content {
            margin-right: var(--sidebar-width);
            padding: 20px;
            transition: all 0.3s;
        }
        
        /* شريط التنقل العلوي */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        
        .search-bar {
            background-color: #f8f9fa;
            border-radius: 30px;
            padding: 8px 20px;
            border: 1px solid #eaeaea;
        }
        
        .search-bar input {
            background: transparent;
            border: none;
            width: 100%;
            padding: 5px 10px;
        }
        
        .search-bar input:focus {
            outline: none;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #f94144;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* تصميم قسم الإعدادات */
        .settings-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .nav-tabs .nav-link {
            color: #495057;
            border: none;
            border-bottom: 2px solid transparent;
            border-radius: 0;
            padding: 10px 15px;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .nav-tabs .nav-link:hover {
            border-color: transparent;
            color: var(--primary-color);
        }
        
        .nav-tabs .nav-link.active {
            color: var(--primary-color);
            border-color: var(--primary-color);
            background-color: transparent;
        }
        
        .tab-content {
            padding: 20px 0;
        }
        
        /* تصميم جدول المستخدمين */
        .users-table .user-role {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .users-table .user-role.admin {
            background-color: #e6f4ea;
            color: #0f9d58;
        }
        
        .users-table .user-role.cashier {
            background-color: #fef7e0;
            color: #f9ab00;
        }
        
        .users-table .user-role.employee {
            background-color: #e8f0fe;
            color: #4285f4;
        }
        
        .user-actions .btn {
            padding: 5px 10px;
            font-size: 14px;
        }
        
        /* تصميم النموذج */
        .modal-header {
            background-color: var(--primary-color);
            color: white;
        }
        
        .modal-footer {
            background-color: #f8f9fa;
        }
        
        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
                overflow: hidden;
            }
            
            .sidebar .logo h3, .sidebar .nav-link span {
                display: none;
            }
            
            .sidebar .nav-link {
                text-align: center;
                margin: 5px 10px;
            }
            
            .sidebar .nav-link i {
                margin-left: 0;
            }
            
            .main-content {
                margin-right: 70px;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-store"></i> متجرنا</h3>
        </div>
        <ul class="nav flex-column mt-4">
            <li class="nav-item">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales.php">
                    <i class="fas fa-shopping-cart"></i>
                    <span>المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales_management.php">
                    <i class="fas fa-chart-line"></i>
                    <span>إدارة المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="products.php">
                    <i class="fas fa-box"></i>
                    <span>المنتجات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customers.php">
                    <i class="fas fa-users"></i>
                    <span>العملاء</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="suppliers.php">
                    <i class="fas fa-truck"></i>
                    <span>الموردين</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="purchases.php">
                    <i class="fas fa-shopping-basket"></i>
                    <span>المشتريات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="expenses.php">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>المصروفات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="cashbox.php">
                    <i class="fas fa-cash-register"></i>
                    <span>الخزنة</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="financial.php">
                    <i class="fas fa-chart-pie"></i>
                    <span>الإدارة المالية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="barcode.php">
                    <i class="fas fa-barcode"></i>
                    <span>مولد الباركود</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="settings.php">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط التنقل العلوي -->
        <div class="top-navbar">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4 class="mb-0">الإعدادات</h4>
                    <p class="mb-0 text-muted">مرحباً بعودتك، <?php echo $_SESSION['username']; ?>!</p>
                </div>
                <div class="col-md-6 d-flex justify-content-end">
                    <div class="d-flex align-items-center">
                        <div class="search-bar me-3">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="بحث...">
                        </div>
                        <div class="position-relative me-3">
                            <button class="btn btn-light rounded-circle">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-light rounded-circle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.php">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- عرض رسائل النجاح والخطأ -->
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <!-- قسم الإعدادات -->
        <div class="settings-container">
            <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="store-tab" data-bs-toggle="tab" data-bs-target="#store" type="button" role="tab" aria-controls="store" aria-selected="true">
                        <i class="fas fa-store me-2"></i>إعدادات المتجر
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab" aria-controls="users" aria-selected="false">
                        <i class="fas fa-users me-2"></i>إدارة المستخدمين
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup" type="button" role="tab" aria-controls="backup" aria-selected="false">
                        <i class="fas fa-database me-2"></i>النسخ الاحتياطي
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab" aria-controls="system" aria-selected="false">
                        <i class="fas fa-cogs me-2"></i>إعدادات النظام
                    </button>
                </li>
            </ul>
            
            <div class="tab-content" id="settingsTabsContent">
                <!-- إعدادات المتجر -->
                <div class="tab-pane fade show active" id="store" role="tabpanel" aria-labelledby="store-tab">
                    <h5 class="mb-4">إعدادات المتجر</h5>
                    <form id="storeSettingsForm" action="" method="post">
                        <input type="hidden" name="update_store_settings" value="1">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="store_name" class="form-label">اسم المتجر <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="store_name" name="store_name" value="<?php echo htmlspecialchars($store_settings['store_name']); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="store_phone" class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" id="store_phone" name="store_phone" value="<?php echo htmlspecialchars($store_settings['store_phone']); ?>">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="store_address" class="form-label">عنوان المتجر</label>
                            <textarea class="form-control" id="store_address" name="store_address" rows="2"><?php echo htmlspecialchars($store_settings['store_address']); ?></textarea>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="tax_rate" class="form-label">نسبة الضريبة (%)</label>
                                <input type="number" class="form-control" id="tax_rate" name="tax_rate" step="0.01" min="0" max="100" value="<?php echo htmlspecialchars($store_settings['tax_rate']); ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="invoice_footer" class="form-label">تذييل الفاتورة</label>
                                <input type="text" class="form-control" id="invoice_footer" name="invoice_footer" value="<?php echo htmlspecialchars($store_settings['invoice_footer']); ?>">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="store_logo" class="form-label">شعار المتجر</label>
                            <input type="file" class="form-control" id="store_logo" name="store_logo">
                            <div class="form-text">الحد الأقصى لحجم الملف: 2 ميجابايت. الأنواع المسموح بها: JPG, PNG, GIF.</div>
                        </div>
                        <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                    </form>
                </div>
                
                <!-- إدارة المستخدمين -->
                <div class="tab-pane fade" id="users" role="tabpanel" aria-labelledby="users-tab">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="mb-0">إدارة المستخدمين</h5>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="fas fa-plus me-1"></i> إضافة مستخدم
                        </button>
                    </div>
                    
                    <div class="users-table">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم المستخدم</th>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الصلاحية</th>
                                        <th>آخر تسجيل دخول</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    if (count($users) > 0) {
                                        $counter = 1;
                                        foreach ($users as $user) {
                                            // تحديد فئة الصلاحية
                                            $role_class = '';
                                            $role_text = '';
                                            
                                            switch ($user['role']) {
                                                case 'admin':
                                                    $role_class = 'admin';
                                                    $role_text = 'مدير';
                                                    break;
                                                case 'cashier':
                                                    $role_class = 'cashier';
                                                    $role_text = 'كاشير';
                                                    break;
                                                case 'employee':
                                                    $role_class = 'employee';
                                                    $role_text = 'موظف';
                                                    break;
                                            }
                                            
                                            echo '<tr>';
                                            echo '<td>' . $counter++ . '</td>';
                                            echo '<td>' . htmlspecialchars($user['username']) . '</td>';
                                            echo '<td>' . htmlspecialchars($user['name']) . '</td>';
                                            echo '<td>' . (!empty($user['email']) ? htmlspecialchars($user['email']) : '-') . '</td>';
                                            echo '<td><span class="user-role ' . $role_class . '">' . $role_text . '</span></td>';
                                            echo '<td>' . (!empty($user['last_login']) ? date('Y-m-d H:i', strtotime($user['last_login'])) : '-') . '</td>';
                                            echo '<td class="user-actions">';
                                            echo '<button class="btn btn-sm btn-info me-1" onclick="editUser(' . $user['id'] . ')" data-bs-toggle="modal" data-bs-target="#editUserModal"><i class="fas fa-edit"></i></button>';
                                            
                                            // لا نعرض زر الحذف للمستخدم الحالي
                                            if ($user['id'] != $_SESSION['user_id']) {
                                                echo '<button class="btn btn-sm btn-danger" onclick="confirmDeleteUser(' . $user['id'] . ')"><i class="fas fa-trash"></i></button>';
                                            }
                                            
                                            echo '</td>';
                                            echo '</tr>';
                                        }
                                    } else {
                                        echo '<tr><td colspan="7" class="text-center">لا يوجد مستخدمين</td></tr>';
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- النسخ الاحتياطي -->
                <div class="tab-pane fade" id="backup" role="tabpanel" aria-labelledby="backup-tab">
                    <h5 class="mb-4">النسخ الاحتياطي واستعادة البيانات</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-download me-2"></i>إنشاء نسخة احتياطية</h6>
                                </div>
                                <div class="card-body">
                                    <p>قم بإنشاء نسخة احتياطية من قاعدة البيانات الحالية. يمكنك تنزيل النسخة الاحتياطية واستخدامها لاستعادة البيانات في وقت لاحق.</p>
                                    <a href="?backup_db=1" class="btn btn-primary">إنشاء نسخة احتياطية</a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-upload me-2"></i>استعادة البيانات</h6>
                                </div>
                                <div class="card-body">
                                    <p>استعادة البيانات من نسخة احتياطية سابقة. سيؤدي ذلك إلى استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.</p>
                                    <form action="" method="post" enctype="multipart/form-data">
                                        <div class="mb-3">
                                            <input type="file" class="form-control" name="backup_file" accept=".sql">
                                        </div>
                                        <button type="submit" class="btn btn-success" name="restore_db">استعادة البيانات</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0"><i class="fas fa-history me-2"></i>النسخ الاحتياطية السابقة</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>اسم الملف</th>
                                            <th>تاريخ الإنشاء</th>
                                            <th>حجم الملف</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // عرض النسخ الاحتياطية السابقة
                                        $backup_files = [];
                                        if (file_exists('backups')) {
                                            $backup_files = array_diff(scandir('backups'), ['.', '..']);
                                        }
                                        
                                        if (count($backup_files) > 0) {
                                            $counter = 1;
                                            foreach ($backup_files as $file) {
                                                $file_path = 'backups/' . $file;
                                                $file_size = filesize($file_path);
                                                $file_date = date('Y-m-d H:i:s', filemtime($file_path));
                                                
                                                echo '<tr>';
                                                echo '<td>' . $counter++ . '</td>';
                                                echo '<td>' . htmlspecialchars($file) . '</td>';
                                                echo '<td>' . $file_date . '</td>';
                                                echo '<td>' . formatFileSize($file_size) . '</td>';
                                                echo '<td>';
                                                echo '<a href="backups/' . $file . '" class="btn btn-sm btn-info me-1" download><i class="fas fa-download"></i></a>';
                                                echo '<button class="btn btn-sm btn-danger" onclick="confirmDeleteBackup(\'' . $file . '\')"><i class="fas fa-trash"></i></button>';
                                                echo '</td>';
                                                echo '</tr>';
                                            }
                                        } else {
                                            echo '<tr><td colspan="5" class="text-center">لا توجد نسخ احتياطية سابقة</td></tr>';
                                        }
                                        
                                        // دالة لتنسيق حجم الملف
                                        function formatFileSize($size) {
                                            $units = ['B', 'KB', 'MB', 'GB', 'TB'];
                                            $i = 0;
                                            while ($size >= 1024 && $i < count($units) - 1) {
                                                $size /= 1024;
                                                $i++;
                                            }
                                            return round($size, 2) . ' ' . $units[$i];
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- إعدادات النظام -->
                <div class="tab-pane fade" id="system" role="tabpanel" aria-labelledby="system-tab">
                    <h5 class="mb-4">إعدادات النظام</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات النظام</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>إصدار النظام</span>
                                            <span class="badge bg-primary">1.0.0</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>إصدار PHP</span>
                                            <span class="badge bg-secondary"><?php echo phpversion(); ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>إصدار MySQL</span>
                                            <span class="badge bg-secondary">
                                                <?php
                                                $mysql_version_query = "SELECT VERSION() as version";
                                                $mysql_version_result = mysqli_query($conn, $mysql_version_query);
                                                $mysql_version = mysqli_fetch_assoc($mysql_version_result)['version'];
                                                echo $mysql_version;
                                                ?>
                                            </span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>نظام التشغيل</span>
                                            <span class="badge bg-secondary"><?php echo php_uname('s') . ' ' . php_uname('r'); ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>الذاكرة المخصصة</span>
                                            <span class="badge bg-secondary"><?php echo ini_get('memory_limit'); ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>الحد الأقصى لحجم الملف</span>
                                            <span class="badge bg-secondary"><?php echo ini_get('upload_max_filesize'); ?></span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-tools me-2"></i>أدوات النظام</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-outline-primary" type="button">
                                            <i class="fas fa-broom me-2"></i>تنظيف ذاكرة التخزين المؤقت
                                        </button>
                                        <button class="btn btn-outline-warning" type="button">
                                            <i class="fas fa-sync-alt me-2"></i>إعادة بناء الفهارس
                                        </button>
                                        <button class="btn btn-outline-info" type="button">
                                            <i class="fas fa-check-circle me-2"></i>فحص سلامة قاعدة البيانات
                                        </button>
                                        <button class="btn btn-outline-danger" type="button">
                                            <i class="fas fa-trash-alt me-2"></i>حذف السجلات القديمة
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>تنبيه</h6>
                                </div>
                                <div class="card-body">
                                    <p>استخدم أدوات النظام بحذر. قد تؤثر بعض العمليات على أداء النظام أو تؤدي إلى فقدان البيانات. يُنصح بإنشاء نسخة احتياطية قبل إجراء أي تغييرات.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال إضافة مستخدم جديد -->
    <div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addUserModalLabel">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm" action="" method="post">
                        <input type="hidden" name="add_user" value="1">
                        <div class="mb-3">
                            <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="name" class="form-label">الاسم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label for="role" class="form-label">الصلاحية <span class="text-danger">*</span></label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="admin">مدير</option>
                                <option value="cashier">كاشير</option>
                                <option value="employee">موظف</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="addUserForm" class="btn btn-primary">إضافة المستخدم</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مودال تعديل مستخدم -->
    <div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editUserModalLabel">تعديل مستخدم</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editUserForm" action="" method="post">
                        <input type="hidden" name="edit_user" value="1">
                        <input type="hidden" name="user_id" id="edit_user_id">
                        <div class="mb-3">
                            <label for="edit_username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="edit_username" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="edit_name" class="form-label">الاسم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="edit_email" name="email">
                        </div>
                        <div class="mb-3">
                            <label for="edit_password" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="edit_password" name="password">
                            <div class="form-text">اترك هذا الحقل فارغًا إذا كنت لا ترغب في تغيير كلمة المرور.</div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_role" class="form-label">الصلاحية <span class="text-danger">*</span></label>
                            <select class="form-select" id="edit_role" name="role" required>
                                <option value="admin">مدير</option>
                                <option value="cashier">كاشير</option>
                                <option value="employee">موظف</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="editUserForm" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // دالة لتعديل المستخدم
        function editUser(userId) {
            // هنا يمكن إضافة كود AJAX لجلب بيانات المستخدم من الخادم
            // لأغراض العرض، سنستخدم بيانات وهمية
            
            // في التطبيق الحقيقي، يجب استبدال هذا بطلب AJAX
            fetch('get_user.php?id=' + userId)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('edit_user_id').value = data.id;
                    document.getElementById('edit_username').value = data.username;
                    document.getElementById('edit_name').value = data.name;
                    document.getElementById('edit_email').value = data.email || '';
                    document.getElementById('edit_role').value = data.role;
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء جلب بيانات المستخدم');
                });
        }
        
        // دالة لتأكيد حذف المستخدم
        function confirmDeleteUser(userId) {
            if (confirm('هل أنت متأكد من رغبتك في حذف هذا المستخدم؟')) {
                window.location.href = 'settings.php?delete_user=' + userId;
            }
        }
        
        // دالة لتأكيد حذف النسخة الاحتياطية
        function confirmDeleteBackup(fileName) {
            if (confirm('هل أنت متأكد من رغبتك في حذف هذه النسخة الاحتياطية؟')) {
                window.location.href = 'settings.php?delete_backup=' + fileName;
            }
        }
    </script>
</body>
</html>