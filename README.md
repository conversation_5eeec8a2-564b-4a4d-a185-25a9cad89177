# نظام إدارة المستخدمين - sqlagentahmed

## نظرة عامة
هذا النظام يوفر واجهة لتسجيل المستخدمين وتسجيل الدخول باستخدام قاعدة بيانات MySQL.

## متطلبات النظام
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)
- XAMPP (يفضل)

## إعداد قاعدة البيانات
1. تأكد من تشغيل خدمات MySQL و Apache في XAMPP
2. افتح المتصفح وانتقل إلى `http://localhost/tec/setup_database.php`
3. سيقوم هذا الملف بإنشاء قاعدة البيانات والجداول المطلوبة

## هيكل قاعدة البيانات
قاعدة البيانات `sqlagentahmed` تحتوي على الجداول التالية:

### جدول المستخدمين (users)
- id: المعرف الفريد للمستخدم (مفتاح أساسي)
- username: اسم المستخدم (فريد)
- password: كلمة المرور (مشفرة)
- email: البريد الإلكتروني (فريد)
- full_name: الاسم الكامل
- registration_date: تاريخ التسجيل
- last_login: آخر تسجيل دخول
- is_active: حالة المستخدم (نشط/غير نشط)

### جدول الجلسات (sessions)
- session_id: معرف الجلسة (مفتاح أساسي)
- user_id: معرف المستخدم (مفتاح أجنبي)
- login_time: وقت تسجيل الدخول
- last_activity: آخر نشاط
- ip_address: عنوان IP
- user_agent: معلومات المتصفح

### جدول سجلات المستخدم (user_logs)
- log_id: معرف السجل (مفتاح أساسي)
- user_id: معرف المستخدم (مفتاح أجنبي)
- action: الإجراء المتخذ
- action_time: وقت الإجراء
- details: تفاصيل إضافية

## اختبار النظام
1. افتح المتصفح وانتقل إلى `http://localhost/tec/test_connection.php` للتأكد من اتصال قاعدة البيانات
2. انتقل إلى `http://localhost/tec/index.html` لتجربة تسجيل الدخول
3. انتقل إلى `http://localhost/tec/register.html` لتجربة إنشاء حساب جديد

## ملاحظات هامة
- تأكد من أن خدمات XAMPP تعمل قبل استخدام النظام
- كلمات المرور مشفرة باستخدام خوارزمية PHP الآمنة
- يتم تسجيل جميع عمليات تسجيل الدخول والخروج في جدول السجلات