<?php
// بدء جلسة
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// استيراد ملف الاتصال بقاعدة البيانات
require_once 'config/db_connection.php';

// تحديد الفترة الزمنية
$period = isset($_GET['period']) ? $_GET['period'] : 'month';
$date_from = '';
$date_to = date('Y-m-d');

switch ($period) {
    case 'day':
        $date_from = date('Y-m-d');
        break;
    case 'week':
        $date_from = date('Y-m-d', strtotime('-7 days'));
        break;
    case 'month':
        $date_from = date('Y-m-d', strtotime('-30 days'));
        break;
    case 'quarter':
        $date_from = date('Y-m-d', strtotime('-90 days'));
        break;
    case 'year':
        $date_from = date('Y-m-d', strtotime('-365 days'));
        break;
    case 'custom':
        $date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-d', strtotime('-30 days'));
        $date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-d');
        break;
}

// تهيئة المتغيرات الإحصائية
$sales_count = 0;
$sales_total = 0;
$sales_profit = 0;
$purchases_count = 0;
$purchases_total = 0;
$expenses_count = 0;
$expenses_total = 0;

// التحقق من وجود جدول الفواتير
$invoices_table_exists_query = "SHOW TABLES LIKE 'invoices'";
$invoices_table_exists_result = mysqli_query($conn, $invoices_table_exists_query);
$invoices_table_exists = mysqli_num_rows($invoices_table_exists_result) > 0;

// التحقق من وجود جدول المشتريات
$purchases_table_exists_query = "SHOW TABLES LIKE 'purchases'";
$purchases_table_exists_result = mysqli_query($conn, $purchases_table_exists_query);
$purchases_table_exists = mysqli_num_rows($purchases_table_exists_result) > 0;

// التحقق من وجود جدول المصروفات
$expenses_table_exists_query = "SHOW TABLES LIKE 'expenses'";
$expenses_table_exists_result = mysqli_query($conn, $expenses_table_exists_query);
$expenses_table_exists = mysqli_num_rows($expenses_table_exists_result) > 0;

// استعلام لجلب إحصائيات المبيعات
if ($invoices_table_exists) {
    $sales_query = "SELECT COUNT(*) as count, SUM(total_amount) as total, SUM(profit) as profit FROM invoices WHERE invoice_date BETWEEN '$date_from' AND '$date_to'";
    $sales_result = mysqli_query($conn, $sales_query);
    if ($sales_result && mysqli_num_rows($sales_result) > 0) {
        $sales_data = mysqli_fetch_assoc($sales_result);
        $sales_count = $sales_data['count'] ?? 0;
        $sales_total = $sales_data['total'] ?? 0;
        $sales_profit = $sales_data['profit'] ?? 0;
    }
}

// استعلام لجلب إحصائيات المشتريات
if ($purchases_table_exists) {
    $purchases_query = "SELECT COUNT(*) as count, SUM(total_amount) as total FROM purchases WHERE purchase_date BETWEEN '$date_from' AND '$date_to'";
    $purchases_result = mysqli_query($conn, $purchases_query);
    if ($purchases_result && mysqli_num_rows($purchases_result) > 0) {
        $purchases_data = mysqli_fetch_assoc($purchases_result);
        $purchases_count = $purchases_data['count'] ?? 0;
        $purchases_total = $purchases_data['total'] ?? 0;
    }
}

// استعلام لجلب إحصائيات المصروفات
if ($expenses_table_exists) {
    $expenses_query = "SELECT COUNT(*) as count, SUM(amount) as total FROM expenses WHERE expense_date BETWEEN '$date_from' AND '$date_to'";
    $expenses_result = mysqli_query($conn, $expenses_query);
    if ($expenses_result && mysqli_num_rows($expenses_result) > 0) {
        $expenses_data = mysqli_fetch_assoc($expenses_result);
        $expenses_count = $expenses_data['count'] ?? 0;
        $expenses_total = $expenses_data['total'] ?? 0;
    }
}

// حساب صافي الربح
$net_profit = $sales_profit - $expenses_total;

// تهيئة مصفوفات البيانات
$daily_sales_data = [];
$daily_expenses_data = [];
$payment_methods_data = [];

// استعلام لجلب بيانات المبيعات اليومية للرسم البياني
if ($invoices_table_exists) {
    $daily_sales_query = "SELECT DATE(invoice_date) as date, SUM(total_amount) as total, SUM(profit) as profit FROM invoices WHERE invoice_date BETWEEN '$date_from' AND '$date_to' GROUP BY DATE(invoice_date) ORDER BY date";
    $daily_sales_result = mysqli_query($conn, $daily_sales_query);
    if ($daily_sales_result) {
        while ($row = mysqli_fetch_assoc($daily_sales_result)) {
            $daily_sales_data[] = $row;
        }
    }
}

// استعلام لجلب بيانات المصروفات اليومية للرسم البياني
if ($expenses_table_exists) {
    $daily_expenses_query = "SELECT DATE(expense_date) as date, SUM(amount) as total FROM expenses WHERE expense_date BETWEEN '$date_from' AND '$date_to' GROUP BY DATE(expense_date) ORDER BY date";
    $daily_expenses_result = mysqli_query($conn, $daily_expenses_query);
    if ($daily_expenses_result) {
        while ($row = mysqli_fetch_assoc($daily_expenses_result)) {
            $daily_expenses_data[] = $row;
        }
    }
}

// استعلام لجلب بيانات المبيعات حسب طريقة الدفع للرسم البياني
if ($invoices_table_exists) {
    $payment_methods_query = "SELECT payment_method, COUNT(*) as count, SUM(total_amount) as total FROM invoices WHERE invoice_date BETWEEN '$date_from' AND '$date_to' GROUP BY payment_method";
    $payment_methods_result = mysqli_query($conn, $payment_methods_query);
    if ($payment_methods_result) {
        while ($row = mysqli_fetch_assoc($payment_methods_result)) {
            $payment_methods_data[] = $row;
        }
    }
}

// تهيئة مصفوفات البيانات الإضافية
$top_products_data = [];
$top_customers_data = [];
$expenses_by_category_data = [];

// التحقق من وجود جدول عناصر الفواتير والمنتجات
$invoice_items_table_exists_query = "SHOW TABLES LIKE 'invoice_items'";
$invoice_items_table_exists_result = mysqli_query($conn, $invoice_items_table_exists_query);
$invoice_items_table_exists = mysqli_num_rows($invoice_items_table_exists_result) > 0;

$products_table_exists_query = "SHOW TABLES LIKE 'products'";
$products_table_exists_result = mysqli_query($conn, $products_table_exists_query);
$products_table_exists = mysqli_num_rows($products_table_exists_result) > 0;

// التحقق من وجود جدول العملاء
$customers_table_exists_query = "SHOW TABLES LIKE 'customers'";
$customers_table_exists_result = mysqli_query($conn, $customers_table_exists_query);
$customers_table_exists = mysqli_num_rows($customers_table_exists_result) > 0;

// استعلام لجلب أفضل المنتجات مبيعًا
if ($invoices_table_exists && $invoice_items_table_exists && $products_table_exists) {
    $top_products_query = "SELECT p.name, SUM(ii.quantity) as total_quantity, SUM(ii.total_price) as total_sales, SUM(ii.profit) as total_profit 
                          FROM invoice_items ii 
                          JOIN products p ON ii.product_id = p.id 
                          JOIN invoices i ON ii.invoice_id = i.id 
                          WHERE i.invoice_date BETWEEN '$date_from' AND '$date_to' 
                          GROUP BY ii.product_id 
                          ORDER BY total_sales DESC 
                          LIMIT 5";
    $top_products_result = mysqli_query($conn, $top_products_query);
    if ($top_products_result) {
        while ($row = mysqli_fetch_assoc($top_products_result)) {
            $top_products_data[] = $row;
        }
    }
}

// استعلام لجلب أفضل العملاء
if ($invoices_table_exists && $customers_table_exists) {
    $top_customers_query = "SELECT c.name, COUNT(i.id) as invoice_count, SUM(i.total_amount) as total_sales 
                           FROM invoices i 
                           JOIN customers c ON i.customer_id = c.id 
                           WHERE i.invoice_date BETWEEN '$date_from' AND '$date_to' 
                           GROUP BY i.customer_id 
                           ORDER BY total_sales DESC 
                           LIMIT 5";
    $top_customers_result = mysqli_query($conn, $top_customers_query);
    if ($top_customers_result) {
        while ($row = mysqli_fetch_assoc($top_customers_result)) {
            $top_customers_data[] = $row;
        }
    }
}

// استعلام لجلب المصروفات حسب الفئة
if ($expenses_table_exists) {
    $expenses_by_category_query = "SELECT category, SUM(amount) as total FROM expenses WHERE expense_date BETWEEN '$date_from' AND '$date_to' GROUP BY category ORDER BY total DESC";
    $expenses_by_category_result = mysqli_query($conn, $expenses_by_category_query);
    if ($expenses_by_category_result) {
        while ($row = mysqli_fetch_assoc($expenses_by_category_result)) {
            $expenses_by_category_data[] = $row;
        }
    }
}

// دالة لتحويل رمز طريقة الدفع إلى نص
function getPaymentMethodText($method) {
    switch ($method) {
        case 'cash':
            return 'نقدي';
        case 'card':
            return 'بطاقة ائتمان';
        case 'bank_transfer':
            return 'تحويل بنكي';
        case 'check':
            return 'شيك';
        case 'credit':
            return 'آجل';
        default:
            return $method;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإدارة المالية - نظام إدارة المتجر</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- ApexCharts -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --sidebar-width: 250px;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fb;
            color: #333;
            overflow-x: hidden;
        }
        
        /* تصميم الشريط الجانبي */
        .sidebar {
            height: 100vh;
            width: var(--sidebar-width);
            position: fixed;
            top: 0;
            right: 0;
            background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding-top: 20px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            z-index: 1000;
        }
        
        .sidebar .logo {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .logo h3 {
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 15px;
            border-radius: 8px;
            transition: all 0.3s;
            font-size: 16px;
        }
        
        .sidebar .nav-link:hover, 
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar .nav-link i {
            margin-left: 10px;
            width: 25px;
            text-align: center;
        }
        
        /* تصميم المحتوى الرئيسي */
        .main-content {
            margin-right: var(--sidebar-width);
            padding: 20px;
            transition: all 0.3s;
        }
        
        /* شريط التنقل العلوي */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        
        .search-bar {
            background-color: #f8f9fa;
            border-radius: 30px;
            padding: 8px 20px;
            border: 1px solid #eaeaea;
        }
        
        .search-bar input {
            background: transparent;
            border: none;
            width: 100%;
            padding: 5px 10px;
        }
        
        .search-bar input:focus {
            outline: none;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #f94144;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* بطاقات الإحصائيات */
        .stat-card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .stat-card .card-title {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-card .card-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .stat-card .card-footer {
            background: transparent;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 13px;
            padding: 8px 15px;
        }
        
        /* تصميم الرسوم البيانية */
        .chart-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        /* تصميم الجداول */
        .table-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .table-container .table th {
            font-weight: 600;
            color: #495057;
        }
        
        .table-container .table td {
            vertical-align: middle;
        }
        
        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
                overflow: hidden;
            }
            
            .sidebar .logo h3, .sidebar .nav-link span {
                display: none;
            }
            
            .sidebar .nav-link {
                text-align: center;
                margin: 5px 10px;
            }
            
            .sidebar .nav-link i {
                margin-left: 0;
            }
            
            .main-content {
                margin-right: 70px;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-store"></i> متجرنا</h3>
        </div>
        <ul class="nav flex-column mt-4">
            <li class="nav-item">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales.php">
                    <i class="fas fa-shopping-cart"></i>
                    <span>المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales_management.php">
                    <i class="fas fa-chart-line"></i>
                    <span>إدارة المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="products.php">
                    <i class="fas fa-box"></i>
                    <span>المنتجات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customers.php">
                    <i class="fas fa-users"></i>
                    <span>العملاء</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="suppliers.php">
                    <i class="fas fa-truck"></i>
                    <span>الموردين</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="purchases.php">
                    <i class="fas fa-shopping-basket"></i>
                    <span>المشتريات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="expenses.php">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>المصروفات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="cashbox.php">
                    <i class="fas fa-cash-register"></i>
                    <span>الخزنة</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="financial.php">
                    <i class="fas fa-chart-pie"></i>
                    <span>الإدارة المالية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="barcode.php">
                    <i class="fas fa-barcode"></i>
                    <span>مولد الباركود</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="settings.php">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط التنقل العلوي -->
        <div class="top-navbar">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4 class="mb-0">الإدارة المالية</h4>
                    <p class="mb-0 text-muted">مرحباً بعودتك، <?php echo $_SESSION['username']; ?>!</p>
                </div>
                <div class="col-md-6 d-flex justify-content-end">
                    <div class="d-flex align-items-center">
                        <div class="search-bar me-3">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="بحث...">
                        </div>
                        <div class="position-relative me-3">
                            <button class="btn btn-light rounded-circle">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-light rounded-circle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.php">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلتر الفترة الزمنية -->
        <div class="chart-container mb-4">
            <div class="chart-header">
                <h5 class="mb-0">تقرير مالي</h5>
                <div class="d-flex">
                    <div class="btn-group me-3">
                        <a href="?period=day" class="btn btn-sm btn-outline-primary <?php echo ($period == 'day') ? 'active' : ''; ?>">اليوم</a>
                        <a href="?period=week" class="btn btn-sm btn-outline-primary <?php echo ($period == 'week') ? 'active' : ''; ?>">الأسبوع</a>
                        <a href="?period=month" class="btn btn-sm btn-outline-primary <?php echo ($period == 'month') ? 'active' : ''; ?>">الشهر</a>
                        <a href="?period=quarter" class="btn btn-sm btn-outline-primary <?php echo ($period == 'quarter') ? 'active' : ''; ?>">الربع</a>
                        <a href="?period=year" class="btn btn-sm btn-outline-primary <?php echo ($period == 'year') ? 'active' : ''; ?>">السنة</a>
                    </div>
                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#customDateRange">
                        <i class="fas fa-calendar-alt me-1"></i> تخصيص
                    </button>
                </div>
            </div>
            
            <div class="collapse <?php echo ($period == 'custom') ? 'show' : ''; ?>" id="customDateRange">
                <div class="card card-body">
                    <form action="" method="get" class="row g-3">
                        <input type="hidden" name="period" value="custom">
                        <div class="col-md-5">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                        </div>
                        <div class="col-md-5">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">تطبيق</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="mt-3">
                <p class="text-muted">
                    الفترة: من <?php echo date('Y-m-d', strtotime($date_from)); ?> إلى <?php echo date('Y-m-d', strtotime($date_to)); ?>
                </p>
            </div>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-primary-subtle text-primary me-3">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div>
                            <div class="card-title">إجمالي المبيعات</div>
                            <div class="card-value"><?php echo number_format($sales_total, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-receipt"></i> <?php echo number_format($sales_count); ?> فاتورة
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-success-subtle text-success me-3">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div>
                            <div class="card-title">إجمالي الأرباح</div>
                            <div class="card-value"><?php echo number_format($sales_profit, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-percentage"></i> نسبة الربح: <?php echo ($sales_total > 0) ? number_format(($sales_profit / $sales_total) * 100, 2) : 0; ?>%
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-danger-subtle text-danger me-3">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div>
                            <div class="card-title">إجمالي المصروفات</div>
                            <div class="card-value"><?php echo number_format($expenses_total, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-receipt"></i> <?php echo number_format($expenses_count); ?> مصروف
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="card-icon bg-warning-subtle text-warning me-3">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <div>
                            <div class="card-title">صافي الربح</div>
                            <div class="card-value"><?php echo number_format($net_profit, 2); ?> ر.س</div>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <i class="fas fa-percentage"></i> نسبة الربح الصافي: <?php echo ($sales_total > 0) ? number_format(($net_profit / $sales_total) * 100, 2) : 0; ?>%
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- الرسم البياني للمبيعات والأرباح -->
            <div class="col-md-8">
                <div class="chart-container">
                    <h5 class="mb-4">المبيعات والأرباح</h5>
                    <div id="salesProfitChart" style="height: 350px;"></div>
                </div>
            </div>
            
            <!-- الرسم البياني لطرق الدفع -->
            <div class="col-md-4">
                <div class="chart-container">
                    <h5 class="mb-4">طرق الدفع</h5>
                    <div id="paymentMethodsChart" style="height: 350px;"></div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- الرسم البياني للمصروفات حسب الفئة -->
            <div class="col-md-6">
                <div class="chart-container">
                    <h5 class="mb-4">المصروفات حسب الفئة</h5>
                    <div id="expensesByCategoryChart" style="height: 300px;"></div>
                </div>
            </div>
            
            <!-- الرسم البياني للإيرادات والمصروفات -->
            <div class="col-md-6">
                <div class="chart-container">
                    <h5 class="mb-4">الإيرادات والمصروفات</h5>
                    <div id="incomeExpensesChart" style="height: 300px;"></div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- أفضل المنتجات مبيعًا -->
            <div class="col-md-6">
                <div class="table-container">
                    <h5 class="mb-4">أفضل المنتجات مبيعًا</h5>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>المبيعات</th>
                                    <th>الربح</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if (count($top_products_data) > 0) {
                                    $counter = 1;
                                    foreach ($top_products_data as $product) {
                                        echo '<tr>';
                                        echo '<td>' . $counter++ . '</td>';
                                        echo '<td>' . htmlspecialchars($product['name']) . '</td>';
                                        echo '<td>' . number_format($product['total_quantity']) . '</td>';
                                        echo '<td>' . number_format($product['total_sales'], 2) . ' ر.س</td>';
                                        echo '<td>' . number_format($product['total_profit'], 2) . ' ر.س</td>';
                                        echo '</tr>';
                                    }
                                } else {
                                    echo '<tr><td colspan="5" class="text-center">لا توجد بيانات متاحة</td></tr>';
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-end">
                        <a href="products.php" class="btn btn-sm btn-outline-primary">عرض كل المنتجات</a>
                    </div>
                </div>
            </div>
            
            <!-- أفضل العملاء -->
            <div class="col-md-6">
                <div class="table-container">
                    <h5 class="mb-4">أفضل العملاء</h5>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>العميل</th>
                                    <th>عدد الفواتير</th>
                                    <th>إجمالي المبيعات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if (count($top_customers_data) > 0) {
                                    $counter = 1;
                                    foreach ($top_customers_data as $customer) {
                                        echo '<tr>';
                                        echo '<td>' . $counter++ . '</td>';
                                        echo '<td>' . htmlspecialchars($customer['name']) . '</td>';
                                        echo '<td>' . number_format($customer['invoice_count']) . '</td>';
                                        echo '<td>' . number_format($customer['total_sales'], 2) . ' ر.س</td>';
                                        echo '</tr>';
                                    }
                                } else {
                                    echo '<tr><td colspan="4" class="text-center">لا توجد بيانات متاحة</td></tr>';
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-end">
                        <a href="customers.php" class="btn btn-sm btn-outline-primary">عرض كل العملاء</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- المصروفات حسب الفئة -->
        <div class="row">
            <div class="col-md-12">
                <div class="table-container">
                    <h5 class="mb-4">المصروفات حسب الفئة</h5>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الفئة</th>
                                    <th>المبلغ</th>
                                    <th>النسبة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if (count($expenses_by_category_data) > 0) {
                                    $counter = 1;
                                    foreach ($expenses_by_category_data as $category) {
                                        $percentage = ($expenses_total > 0) ? ($category['total'] / $expenses_total) * 100 : 0;
                                        echo '<tr>';
                                        echo '<td>' . $counter++ . '</td>';
                                        echo '<td>' . htmlspecialchars($category['category']) . '</td>';
                                        echo '<td>' . number_format($category['total'], 2) . ' ر.س</td>';
                                        echo '<td>' . number_format($percentage, 2) . '%</td>';
                                        echo '</tr>';
                                    }
                                } else {
                                    echo '<tr><td colspan="4" class="text-center">لا توجد بيانات متاحة</td></tr>';
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-end">
                        <a href="expenses.php" class="btn btn-sm btn-outline-primary">عرض كل المصروفات</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // بيانات المبيعات والأرباح
            const salesProfitData = {
                dates: [
                    <?php
                    foreach ($daily_sales_data as $data) {
                        echo "'" . date('Y-m-d', strtotime($data['date'])) . "',";
                    }
                    ?>
                ],
                sales: [
                    <?php
                    foreach ($daily_sales_data as $data) {
                        echo $data['total'] . ",";
                    }
                    ?>
                ],
                profits: [
                    <?php
                    foreach ($daily_sales_data as $data) {
                        echo $data['profit'] . ",";
                    }
                    ?>
                ]
            };
            
            // بيانات طرق الدفع
            const paymentMethodsData = {
                methods: [
                    <?php
                    foreach ($payment_methods_data as $data) {
                        echo "'" . getPaymentMethodText($data['payment_method']) . "',";
                    }
                    ?>
                ],
                values: [
                    <?php
                    foreach ($payment_methods_data as $data) {
                        echo $data['total'] . ",";
                    }
                    ?>
                ]
            };
            
            // بيانات المصروفات حسب الفئة
            const expensesByCategoryData = {
                categories: [
                    <?php
                    foreach ($expenses_by_category_data as $data) {
                        echo "'" . addslashes($data['category']) . "',";
                    }
                    ?>
                ],
                values: [
                    <?php
                    foreach ($expenses_by_category_data as $data) {
                        echo $data['total'] . ",";
                    }
                    ?>
                ]
            };
            
            // بيانات الإيرادات والمصروفات
            const incomeExpensesData = {
                dates: [
                    <?php
                    // إنشاء مصفوفة بجميع التواريخ
                    $all_dates = [];
                    foreach ($daily_sales_data as $data) {
                        $all_dates[] = $data['date'];
                    }
                    foreach ($daily_expenses_data as $data) {
                        if (!in_array($data['date'], $all_dates)) {
                            $all_dates[] = $data['date'];
                        }
                    }
                    sort($all_dates);
                    
                    foreach ($all_dates as $date) {
                        echo "'" . date('Y-m-d', strtotime($date)) . "',";
                    }
                    ?>
                ],
                income: [
                    <?php
                    // إنشاء مصفوفة بالإيرادات لكل تاريخ
                    $income_by_date = [];
                    foreach ($daily_sales_data as $data) {
                        $income_by_date[$data['date']] = $data['profit'];
                    }
                    
                    foreach ($all_dates as $date) {
                        echo isset($income_by_date[$date]) ? $income_by_date[$date] : 0;
                        echo ",";
                    }
                    ?>
                ],
                expenses: [
                    <?php
                    // إنشاء مصفوفة بالمصروفات لكل تاريخ
                    $expenses_by_date = [];
                    foreach ($daily_expenses_data as $data) {
                        $expenses_by_date[$data['date']] = $data['total'];
                    }
                    
                    foreach ($all_dates as $date) {
                        echo isset($expenses_by_date[$date]) ? $expenses_by_date[$date] : 0;
                        echo ",";
                    }
                    ?>
                ]
            };
            
            // رسم بياني للمبيعات والأرباح
            const salesProfitOptions = {
                series: [{
                    name: 'المبيعات',
                    type: 'column',
                    data: salesProfitData.sales
                }, {
                    name: 'الأرباح',
                    type: 'line',
                    data: salesProfitData.profits
                }],
                chart: {
                    height: 350,
                    type: 'line',
                    fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
                    toolbar: {
                        show: false
                    }
                },
                stroke: {
                    width: [0, 4]
                },
                dataLabels: {
                    enabled: true,
                    enabledOnSeries: [1]
                },
                labels: salesProfitData.dates,
                xaxis: {
                    type: 'datetime'
                },
                yaxis: [{
                    title: {
                        text: 'المبيعات (ر.س)',
                    },
                }, {
                    opposite: true,
                    title: {
                        text: 'الأرباح (ر.س)'
                    }
                }],
                colors: ['#4361ee', '#28a745'],
                tooltip: {
                    y: {
                        formatter: function(value) {
                            return value.toFixed(2) + ' ر.س';
                        }
                    }
                }
            };
            
            const salesProfitChart = new ApexCharts(document.querySelector("#salesProfitChart"), salesProfitOptions);
            salesProfitChart.render();
            
            // رسم بياني لطرق الدفع
            const paymentMethodsOptions = {
                series: paymentMethodsData.values,
                chart: {
                    type: 'pie',
                    height: 350,
                    fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
                },
                labels: paymentMethodsData.methods,
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            width: 200
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }],
                colors: ['#4361ee', '#3f37c9', '#4cc9f0', '#ff9e00', '#f94144'],
                tooltip: {
                    y: {
                        formatter: function(value) {
                            return value.toFixed(2) + ' ر.س';
                        }
                    }
                }
            };
            
            const paymentMethodsChart = new ApexCharts(document.querySelector("#paymentMethodsChart"), paymentMethodsOptions);
            paymentMethodsChart.render();
            
            // رسم بياني للمصروفات حسب الفئة
            const expensesByCategoryOptions = {
                series: [{
                    name: 'المبلغ',
                    data: expensesByCategoryData.values
                }],
                chart: {
                    type: 'bar',
                    height: 300,
                    fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
                    toolbar: {
                        show: false
                    }
                },
                plotOptions: {
                    bar: {
                        horizontal: true,
                        columnWidth: '55%',
                        endingShape: 'rounded'
                    },
                },
                dataLabels: {
                    enabled: false
                },
                xaxis: {
                    categories: expensesByCategoryData.categories,
                },
                colors: ['#f94144'],
                tooltip: {
                    y: {
                        formatter: function(value) {
                            return value.toFixed(2) + ' ر.س';
                        }
                    }
                }
            };
            
            const expensesByCategoryChart = new ApexCharts(document.querySelector("#expensesByCategoryChart"), expensesByCategoryOptions);
            expensesByCategoryChart.render();
            
            // رسم بياني للإيرادات والمصروفات
            const incomeExpensesOptions = {
                series: [{
                    name: 'الأرباح',
                    data: incomeExpensesData.income
                }, {
                    name: 'المصروفات',
                    data: incomeExpensesData.expenses
                }],
                chart: {
                    type: 'area',
                    height: 300,
                    fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
                    toolbar: {
                        show: false
                    },
                    stacked: false
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth',
                    width: 2
                },
                fill: {
                    type: 'gradient',
                    gradient: {
                        opacityFrom: 0.6,
                        opacityTo: 0.1,
                    }
                },
                legend: {
                    position: 'top',
                    horizontalAlign: 'right'
                },
                xaxis: {
                    type: 'datetime',
                    categories: incomeExpensesData.dates
                },
                colors: ['#28a745', '#f94144'],
                tooltip: {
                    y: {
                        formatter: function(value) {
                            return value.toFixed(2) + ' ر.س';
                        }
                    }
                }
            };
            
            const incomeExpensesChart = new ApexCharts(document.querySelector("#incomeExpensesChart"), incomeExpensesOptions);
            incomeExpensesChart.render();
        });
    </script>
</body>
</html>