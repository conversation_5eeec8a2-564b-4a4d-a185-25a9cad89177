<?php
// Include database connection
require_once 'db_connect.php';

// Check if the form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $fullName = $_POST['fullName'] ?? '';
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    // Check if we're receiving data from the HTML form with different field names
    if (empty($email) && isset($_POST['registerEmail'])) {
        $email = $_POST['registerEmail'];
    }
    
    if (empty($password) && isset($_POST['registerPassword'])) {
        $password = $_POST['registerPassword'];
    }
    
    // Validate input
    $errors = [];
    
    if (empty($fullName)) {
        $errors[] = "الاسم الكامل مطلوب";
    }
    
    if (empty($email)) {
        $errors[] = "البريد الإلكتروني مطلوب";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "صيغة البريد الإلكتروني غير صحيحة";
    }
    
    if (empty($password)) {
        $errors[] = "كلمة المرور مطلوبة";
    } elseif (strlen($password) < 6) {
        $errors[] = "كلمة المرور يجب أن تكون على الأقل 6 أحرف";
    }
    
    // If no errors, proceed with registration
    if (empty($errors)) {
        // Check if email already exists
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $response = [
                'success' => false,
                'message' => 'البريد الإلكتروني مسجل بالفعل'
            ];
        } else {
            // Hash the password
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            
            // Insert new user
            $stmt = $conn->prepare("INSERT INTO users (username, password, email, full_name) VALUES (?, ?, ?, ?)");
            $username = explode('@', $email)[0]; // Generate username from email
            $stmt->bind_param("ssss", $username, $hashedPassword, $email, $fullName);
            
            if ($stmt->execute()) {
                $response = [
                    'success' => true,
                    'message' => 'تم إنشاء الحساب بنجاح'
                ];
            } else {
                $response = [
                    'success' => false,
                    'message' => 'حدث خطأ أثناء إنشاء الحساب: ' . $stmt->error
                ];
            }
        }
        $stmt->close();
    } else {
        $response = [
            'success' => false,
            'message' => 'يرجى تصحيح الأخطاء التالية:',
            'errors' => $errors
        ];
    }
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode($response);
    
    // Close connection
    closeConnection($conn);
    exit;
}
?>