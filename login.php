<?php
// Include database connection
require_once 'db_connect.php';

// Start session
session_start();

// Check if the form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['rememberMe']) ? true : false;
    
    // Validate input
    $errors = [];
    
    if (empty($email)) {
        $errors[] = "البريد الإلكتروني مطلوب";
    }
    
    if (empty($password)) {
        $errors[] = "كلمة المرور مطلوبة";
    }
    
    // If no errors, proceed with login
    if (empty($errors)) {
        // Check if user exists
        $stmt = $conn->prepare("SELECT id, username, password, full_name FROM users WHERE email = ?");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 1) {
            $user = $result->fetch_assoc();
            
            // Verify password
            if (password_verify($password, $user['password'])) {
                // Password is correct, create session
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                
                // Update last login time
                $updateStmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                $updateStmt->bind_param("i", $user['id']);
                $updateStmt->execute();
                $updateStmt->close();
                
                // Create session record
                $sessionId = session_id();
                $userId = $user['id'];
                $ipAddress = $_SERVER['REMOTE_ADDR'];
                $userAgent = $_SERVER['HTTP_USER_AGENT'];
                
                $sessionStmt = $conn->prepare("INSERT INTO sessions (session_id, user_id, ip_address, user_agent) VALUES (?, ?, ?, ?)");
                $sessionStmt->bind_param("siss", $sessionId, $userId, $ipAddress, $userAgent);
                $sessionStmt->execute();
                $sessionStmt->close();
                
                // Log user action
                $action = "تسجيل الدخول";
                $details = "تم تسجيل الدخول بنجاح";
                
                $logStmt = $conn->prepare("INSERT INTO user_logs (user_id, action, details) VALUES (?, ?, ?)");
                $logStmt->bind_param("iss", $userId, $action, $details);
                $logStmt->execute();
                $logStmt->close();
                
                $response = [
                    'success' => true,
                    'message' => 'تم تسجيل الدخول بنجاح',
                    'redirect' => 'dashboard.php'
                ];
            } else {
                $response = [
                    'success' => false,
                    'message' => 'كلمة المرور غير صحيحة'
                ];
            }
        } else {
            $response = [
                'success' => false,
                'message' => 'البريد الإلكتروني غير مسجل'
            ];
        }
        $stmt->close();
    } else {
        $response = [
            'success' => false,
            'message' => 'يرجى تصحيح الأخطاء التالية:',
            'errors' => $errors
        ];
    }
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode($response);
    
    // Close connection
    closeConnection($conn);
    exit;
}
?>