<?php
// بدء جلسة
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// استيراد ملف الاتصال بقاعدة البيانات
require_once 'config/db_connection.php';

// استعلام لجلب المنتجات
$products_query = "SELECT id, name, barcode, price FROM products WHERE barcode IS NOT NULL AND barcode != '' ORDER BY name";
$products_result = mysqli_query($conn, $products_query);
$products = [];
while ($product = mysqli_fetch_assoc($products_result)) {
    $products[] = $product;
}

// معالجة طلب إنشاء باركود جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['generate_barcode'])) {
    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
    $barcode_value = isset($_POST['barcode_value']) ? mysqli_real_escape_string($conn, $_POST['barcode_value']) : '';
    
    if (!empty($barcode_value) && $product_id > 0) {
        // التحقق من عدم تكرار الباركود
        $check_barcode_query = "SELECT id FROM products WHERE barcode = '$barcode_value' AND id != $product_id";
        $check_barcode_result = mysqli_query($conn, $check_barcode_query);
        
        if (mysqli_num_rows($check_barcode_result) > 0) {
            $error_message = "الباركود مستخدم بالفعل، يرجى استخدام باركود آخر.";
        } else {
            // تحديث الباركود للمنتج
            $update_query = "UPDATE products SET barcode = '$barcode_value' WHERE id = $product_id";
            
            if (mysqli_query($conn, $update_query)) {
                $success_message = "تم تحديث الباركود بنجاح.";
                // إعادة تحميل الصفحة لتحديث البيانات
                header("Location: barcode.php?success=1");
                exit();
            } else {
                $error_message = "حدث خطأ أثناء تحديث الباركود: " . mysqli_error($conn);
            }
        }
    } else {
        $error_message = "يرجى اختيار منتج وإدخال قيمة الباركود.";
    }
}

// رسائل النجاح
if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case 1:
            $success_message = "تم تحديث الباركود بنجاح.";
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد الباركود - نظام إدارة المتجر</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- JsBarcode -->
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --sidebar-width: 250px;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fb;
            color: #333;
            overflow-x: hidden;
        }
        
        /* تصميم الشريط الجانبي */
        .sidebar {
            height: 100vh;
            width: var(--sidebar-width);
            position: fixed;
            top: 0;
            right: 0;
            background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding-top: 20px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            z-index: 1000;
        }
        
        .sidebar .logo {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .logo h3 {
            font-weight: 700;
            margin-bottom: 0;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 15px;
            border-radius: 8px;
            transition: all 0.3s;
            font-size: 16px;
        }
        
        .sidebar .nav-link:hover, 
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar .nav-link i {
            margin-left: 10px;
            width: 25px;
            text-align: center;
        }
        
        /* تصميم المحتوى الرئيسي */
        .main-content {
            margin-right: var(--sidebar-width);
            padding: 20px;
            transition: all 0.3s;
        }
        
        /* شريط التنقل العلوي */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        
        .search-bar {
            background-color: #f8f9fa;
            border-radius: 30px;
            padding: 8px 20px;
            border: 1px solid #eaeaea;
        }
        
        .search-bar input {
            background: transparent;
            border: none;
            width: 100%;
            padding: 5px 10px;
        }
        
        .search-bar input:focus {
            outline: none;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #f94144;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* تصميم قسم الباركود */
        .barcode-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .barcode-generator {
            background-color: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .barcode-preview {
            background-color: white;
            border: 1px solid #eaeaea;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .barcode-item {
            background-color: white;
            border: 1px solid #eaeaea;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: transform 0.3s;
        }
        
        .barcode-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .barcode-item .product-info {
            margin-bottom: 10px;
        }
        
        .barcode-item .barcode-image {
            text-align: center;
            margin-bottom: 10px;
        }
        
        .barcode-item .barcode-actions {
            display: flex;
            justify-content: center;
            gap: 10px;
        }
        
        .print-options {
            margin-bottom: 20px;
        }
        
        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
                overflow: hidden;
            }
            
            .sidebar .logo h3, .sidebar .nav-link span {
                display: none;
            }
            
            .sidebar .nav-link {
                text-align: center;
                margin: 5px 10px;
            }
            
            .sidebar .nav-link i {
                margin-left: 0;
            }
            
            .main-content {
                margin-right: 70px;
            }
        }
        
        /* تصميم صفحة الطباعة */
        @media print {
            .sidebar, .top-navbar, .barcode-generator, .print-options, .barcode-actions, .no-print {
                display: none !important;
            }
            
            .main-content {
                margin: 0;
                padding: 0;
            }
            
            .barcode-container {
                box-shadow: none;
                padding: 0;
            }
            
            .barcode-item {
                break-inside: avoid;
                border: none;
                padding: 5mm;
                margin: 0;
                page-break-inside: avoid;
            }
            
            .barcode-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 5mm;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-store"></i> متجرنا</h3>
        </div>
        <ul class="nav flex-column mt-4">
            <li class="nav-item">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales.php">
                    <i class="fas fa-shopping-cart"></i>
                    <span>المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sales_management.php">
                    <i class="fas fa-chart-line"></i>
                    <span>إدارة المبيعات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="products.php">
                    <i class="fas fa-box"></i>
                    <span>المنتجات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customers.php">
                    <i class="fas fa-users"></i>
                    <span>العملاء</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="suppliers.php">
                    <i class="fas fa-truck"></i>
                    <span>الموردين</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="purchases.php">
                    <i class="fas fa-shopping-basket"></i>
                    <span>المشتريات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="expenses.php">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>المصروفات</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="cashbox.php">
                    <i class="fas fa-cash-register"></i>
                    <span>الخزنة</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="financial.php">
                    <i class="fas fa-chart-pie"></i>
                    <span>الإدارة المالية</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="barcode.php">
                    <i class="fas fa-barcode"></i>
                    <span>مولد الباركود</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="settings.php">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط التنقل العلوي -->
        <div class="top-navbar">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4 class="mb-0">مولد الباركود</h4>
                    <p class="mb-0 text-muted">مرحباً بعودتك، <?php echo $_SESSION['username']; ?>!</p>
                </div>
                <div class="col-md-6 d-flex justify-content-end">
                    <div class="d-flex align-items-center">
                        <div class="search-bar me-3">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="بحث...">
                        </div>
                        <div class="position-relative me-3">
                            <button class="btn btn-light rounded-circle">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-light rounded-circle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.php">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- عرض رسائل النجاح والخطأ -->
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-4">
                <!-- مولد الباركود -->
                <div class="barcode-generator">
                    <h5 class="mb-3">إنشاء باركود جديد</h5>
                    <form action="" method="post">
                        <input type="hidden" name="generate_barcode" value="1">
                        <div class="mb-3">
                            <label for="product_id" class="form-label">اختر المنتج</label>
                            <select class="form-select" id="product_id" name="product_id" required>
                                <option value="">اختر المنتج</option>
                                <?php
                                // استعلام لجلب المنتجات التي ليس لها باركود
                                $no_barcode_query = "SELECT id, name FROM products WHERE barcode IS NULL OR barcode = '' ORDER BY name";
                                $no_barcode_result = mysqli_query($conn, $no_barcode_query);
                                
                                while ($product = mysqli_fetch_assoc($no_barcode_result)) {
                                    echo '<option value="' . $product['id'] . '">' . htmlspecialchars($product['name']) . '</option>';
                                }
                                ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="barcode_value" class="form-label">قيمة الباركود</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="barcode_value" name="barcode_value" required>
                                <button type="button" class="btn btn-outline-secondary" id="generateRandom">توليد تلقائي</button>
                            </div>
                            <div class="form-text">يمكنك إدخال قيمة الباركود يدويًا أو توليدها تلقائيًا.</div>
                        </div>
                        <div class="mb-3">
                            <label for="barcode_format" class="form-label">نوع الباركود</label>
                            <select class="form-select" id="barcode_format">
                                <option value="CODE128">CODE128 (الافتراضي)</option>
                                <option value="EAN13">EAN-13</option>
                                <option value="EAN8">EAN-8</option>
                                <option value="UPC">UPC</option>
                                <option value="CODE39">CODE39</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">معاينة</label>
                            <div class="barcode-preview">
                                <svg id="barcodePreview"></svg>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">حفظ الباركود</button>
                    </form>
                </div>
            </div>
            
            <div class="col-md-8">
                <!-- قائمة الباركود -->
                <div class="barcode-container">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="mb-0">الباركود المتاح</h5>
                        <div class="print-options no-print">
                            <button class="btn btn-primary" onclick="window.print()">
                                <i class="fas fa-print me-1"></i> طباعة الكل
                            </button>
                        </div>
                    </div>
                    
                    <div class="barcode-grid">
                        <?php if (count($products) > 0): ?>
                            <?php foreach ($products as $product): ?>
                                <div class="barcode-item">
                                    <div class="product-info">
                                        <h6><?php echo htmlspecialchars($product['name']); ?></h6>
                                        <p class="mb-1">السعر: <?php echo number_format($product['price'], 2); ?> ر.س</p>
                                    </div>
                                    <div class="barcode-image">
                                        <svg class="barcode" data-value="<?php echo $product['barcode']; ?>"></svg>
                                    </div>
                                    <div class="barcode-actions no-print">
                                        <button class="btn btn-sm btn-outline-primary print-single">
                                            <i class="fas fa-print me-1"></i> طباعة
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="copyBarcode('<?php echo $product['barcode']; ?>')">
                                            <i class="fas fa-copy me-1"></i> نسخ
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center p-4">
                                <p>لا توجد منتجات بباركود متاح.</p>
                                <p>يمكنك إضافة باركود للمنتجات من خلال النموذج المجاور.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // توليد الباركود للمعاينة
            const barcodeValue = document.getElementById('barcode_value');
            const barcodeFormat = document.getElementById('barcode_format');
            const barcodePreview = document.getElementById('barcodePreview');
            
            function updatePreview() {
                if (barcodeValue.value) {
                    try {
                        JsBarcode("#barcodePreview", barcodeValue.value, {
                            format: barcodeFormat.value,
                            width: 2,
                            height: 50,
                            displayValue: true,
                            fontSize: 14,
                            margin: 10
                        });
                    } catch (e) {
                        console.error(e);
                        barcodePreview.innerHTML = '<text x="50%" y="50%" text-anchor="middle" fill="red">خطأ في توليد الباركود</text>';
                    }
                } else {
                    barcodePreview.innerHTML = '<text x="50%" y="50%" text-anchor="middle" fill="gray">أدخل قيمة الباركود للمعاينة</text>';
                }
            }
            
            barcodeValue.addEventListener('input', updatePreview);
            barcodeFormat.addEventListener('change', updatePreview);
            
            // توليد رقم باركود عشوائي
            document.getElementById('generateRandom').addEventListener('click', function() {
                const format = barcodeFormat.value;
                let randomBarcode = '';
                
                if (format === 'EAN13') {
                    // توليد 12 رقم (الرقم الـ 13 هو رقم التحقق)
                    for (let i = 0; i < 12; i++) {
                        randomBarcode += Math.floor(Math.random() * 10);
                    }
                } else if (format === 'EAN8') {
                    // توليد 7 أرقام (الرقم الـ 8 هو رقم التحقق)
                    for (let i = 0; i < 7; i++) {
                        randomBarcode += Math.floor(Math.random() * 10);
                    }
                } else if (format === 'UPC') {
                    // توليد 11 رقم (الرقم الـ 12 هو رقم التحقق)
                    for (let i = 0; i < 11; i++) {
                        randomBarcode += Math.floor(Math.random() * 10);
                    }
                } else if (format === 'CODE39') {
                    // توليد 8 أحرف وأرقام
                    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                    for (let i = 0; i < 8; i++) {
                        randomBarcode += chars.charAt(Math.floor(Math.random() * chars.length));
                    }
                } else {
                    // CODE128 (الافتراضي) - توليد 10 أرقام
                    for (let i = 0; i < 10; i++) {
                        randomBarcode += Math.floor(Math.random() * 10);
                    }
                }
                
                barcodeValue.value = randomBarcode;
                updatePreview();
            });
            
            // توليد الباركود للمنتجات المعروضة
            const barcodes = document.querySelectorAll('.barcode');
            barcodes.forEach(function(barcode) {
                const value = barcode.getAttribute('data-value');
                if (value) {
                    JsBarcode(barcode, value, {
                        format: "CODE128",
                        width: 2,
                        height: 50,
                        displayValue: true,
                        fontSize: 14,
                        margin: 10
                    });
                }
            });
            
            // طباعة باركود واحد
            const printButtons = document.querySelectorAll('.print-single');
            printButtons.forEach(function(button) {
                button.addEventListener('click', function() {
                    const barcodeItem = this.closest('.barcode-item');
                    const barcodeItemClone = barcodeItem.cloneNode(true);
                    
                    // إنشاء نافذة طباعة جديدة
                    const printWindow = window.open('', '_blank');
                    printWindow.document.write(`
                        <html>
                        <head>
                            <title>طباعة الباركود</title>
                            <style>
                                body {
                                    font-family: Arial, sans-serif;
                                    margin: 0;
                                    padding: 20px;
                                }
                                .barcode-item {
                                    text-align: center;
                                    padding: 10mm;
                                    border: 1px solid #eee;
                                    margin: 0 auto;
                                    max-width: 80mm;
                                }
                                .barcode-actions {
                                    display: none;
                                }
                            </style>
                        </head>
                        <body>
                            ${barcodeItemClone.outerHTML}
                            <script>
                                window.onload = function() {
                                    setTimeout(function() {
                                        window.print();
                                        window.close();
                                    }, 500);
                                };
                            </script>
                        </body>
                        </html>
                    `);
                    printWindow.document.close();
                });
            });
        });
        
        // نسخ الباركود إلى الحافظة
        function copyBarcode(barcode) {
            navigator.clipboard.writeText(barcode).then(function() {
                alert('تم نسخ الباركود: ' + barcode);
            }, function(err) {
                console.error('فشل نسخ الباركود: ', err);
            });
        }
    </script>
</body>
</html>