<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database connection
require_once 'db_connect.php';

echo "<h1>تشخيص النظام</h1>";

// Check database connection
echo "<h2>اتصال قاعدة البيانات</h2>";
if ($conn && !$conn->connect_error) {
    echo "<p style='color:green'>الاتصال بقاعدة البيانات يعمل بشكل صحيح</p>";
} else {
    echo "<p style='color:red'>فشل الاتصال بقاعدة البيانات: " . $conn->connect_error . "</p>";
}

// Check if database exists
echo "<h2>قاعدة البيانات</h2>";
$result = $conn->query("SHOW DATABASES LIKE 'sqlagentahmed'");
if ($result->num_rows > 0) {
    echo "<p style='color:green'>قاعدة البيانات 'sqlagentahmed' موجودة</p>";
} else {
    echo "<p style='color:red'>قاعدة البيانات 'sqlagentahmed' غير موجودة</p>";
}

// Select the database
$conn->select_db("sqlagentahmed");

// Check if tables exist
echo "<h2>الجداول</h2>";
$tables = array("users", "sessions", "user_logs");
foreach ($tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "<p style='color:green'>جدول '$table' موجود</p>";
        
        // Show table structure
        echo "<h3>هيكل جدول '$table'</h3>";
        $structure = $conn->query("DESCRIBE $table");
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>الحقل</th><th>النوع</th><th>Null</th><th>المفتاح</th><th>الافتراضي</th><th>إضافي</th></tr>";
        while ($row = $structure->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color:red'>جدول '$table' غير موجود</p>";
        
        // Create the table if it doesn't exist
        echo "<p>جاري إنشاء جدول '$table'...</p>";
        
        if ($table == "users") {
            $sql = "CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                email VARCHAR(100) NOT NULL UNIQUE,
                full_name VARCHAR(100),
                registration_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME,
                is_active BOOLEAN DEFAULT TRUE
            )";
        } elseif ($table == "sessions") {
            $sql = "CREATE TABLE IF NOT EXISTS sessions (
                session_id VARCHAR(128) PRIMARY KEY,
                user_id INT,
                login_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
                ip_address VARCHAR(45),
                user_agent TEXT,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )";
        } elseif ($table == "user_logs") {
            $sql = "CREATE TABLE IF NOT EXISTS user_logs (
                log_id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT,
                action VARCHAR(100) NOT NULL,
                action_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                details TEXT,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
            )";
        }
        
        if ($conn->query($sql) === TRUE) {
            echo "<p style='color:green'>تم إنشاء جدول '$table' بنجاح</p>";
        } else {
            echo "<p style='color:red'>خطأ في إنشاء جدول '$table': " . $conn->error . "</p>";
        }
    }
}

// Close connection
closeConnection($conn);
?>