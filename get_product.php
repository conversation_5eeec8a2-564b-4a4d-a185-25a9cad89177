<?php
// بدء جلسة
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

// استيراد ملف الاتصال بقاعدة البيانات
require_once 'config/db_connection.php';

// التحقق من وجود معرف المنتج
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'معرف المنتج مطلوب']);
    exit();
}

$product_id = intval($_GET['id']);

// استعلام لجلب بيانات المنتج
$product_query = "SELECT * FROM products WHERE id = $product_id";
$product_result = mysqli_query($conn, $product_query);

if (mysqli_num_rows($product_result) > 0) {
    $product = mysqli_fetch_assoc($product_result);
    
    // تحويل البيانات إلى JSON
    header('Content-Type: application/json');
    echo json_encode($product);
} else {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'المنتج غير موجود']);
}